import { buttonVariants } from "@/components/ui/button";
import ReviewsCard from "@/components/ux/review/review-card";
import { aldineBT } from "@/constants/fonts";
import { supabaseServer } from "@/lib/supabase/server";
import { cn } from "@/lib/utils";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

export const revalidate = 0;

export const metadata: Metadata = {
  title: "Reviews",
  description: "What Our Customers Have Got To Say About Us.",
  openGraph: {
    title: "Reviews | Black Cherry",
    description: "What Our Customers Have Got To Say About Us.",
    url: "https://www.blackcherrygh.com/reviews/",
    siteName: "Reviews | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Reviews | Black Cherry",
    description: "What Our Customers Have Got To Say About Us.",
    site: "https://www.blackcherrygh.com/reviews/",
  },
};

export default async function Page() {
  const supabase = await supabaseServer();

  const { data } = await supabase
    .from("reviews")
    .select("*")
    .eq("is_approved", true);
  return (
    <main>
      <div className="mx-auto flex w-full max-w-6xl flex-col items-center px-4 pt-20">
        {/* hero section */}
        <section className="mt-10 mb-4 flex w-full flex-col items-center justify-center space-y-2 text-center">
          <h2 className={cn(aldineBT.className, "text-4xl md:text-6xl")}>
            Reviews
          </h2>
          <p>
            Discover What Our Customers Have <br /> Got To Say About Us
          </p>
        </section>
        {/* reviews */}
        <section className="flex h-full min-h-[32rem] w-full flex-col">
          {!data || data.length === 0 ? (
            <div className="flex w-full flex-col items-center space-y-4 py-10 text-center">
              <p>No Reviews</p>
              <Link
                href={"/reviews/new"}
                className={cn(
                  buttonVariants({
                    variant: "default",
                    size: "withIconRight",
                  }),
                  "group space-x-2 bg-black text-white hover:bg-gray-900",
                )}
              >
                <span>Leave A Review</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-white" />
                </div>
              </Link>
            </div>
          ) : (
            <div className="mx-auto flex w-full max-w-4xl flex-col p-4">
              <div className="grid w-full flex-wrap md:flex">
                {data.map((d, i) => {
                  return <ReviewsCard key={i} r={d} />;
                })}
              </div>
            </div>
          )}
        </section>
      </div>
    </main>
  );
}
