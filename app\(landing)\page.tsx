import { Brands } from "@/components/ux/home/<USER>";
import FAQS from "@/components/ux/home/<USER>";
import HomeHero from "@/components/ux/home/<USER>";
import OtherSections from "@/components/ux/home/<USER>";
import { FounderSection } from "@/components/ux/home/<USER>";
import {
  getAllSeyNaturelleProducts,
  getImageComponentBySlug,
} from "@/lib/sanity/lib/actions";
import { imageComponent, seyNaturelleProductdType } from "@/types/types";
import aL from "@/public/images/pc-l.png";
import Image from "next/image";
import { Noise } from "@/components/ux/animations/noise";
import { getCVData } from "@/lib/cv-content";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";

export const revalidate = 30;

export default async function Home() {
  // const imageComponent: imageComponent =
  //   await getImageComponentBySlug("home-carousel");

  // const seyNaturelleProducts: seyNaturelleProductdType[] =
  //   await getAllSeyNaturelleProducts();
  const cv = getCVData();
  return (
    <main>
      <section className="relative z-10 flex h-[44rem] w-full flex-col items-center justify-center overflow-hidden">
        <Noise />
        <Image
          alt="home"
          src={aL}
          className="absolute inset-0 z-0 h-[44rem] object-cover object-[80%] md:object-left"
        />
        <div className="relative z-10 flex h-[44rem] w-full max-w-6xl flex-col justify-end p-4">
          <div className="flex flex-col">
            <h1
              className={cn(
                aldineBT.className,
                "text-4xl text-white md:text-6xl",
              )}
            >
              {cv.personalInfo.name}
            </h1>
          </div>
        </div>
      </section>
    </main>
  );
}
