import { Brands } from "@/components/ux/home/<USER>";
import FAQS from "@/components/ux/home/<USER>";
import HomeHero from "@/components/ux/home/<USER>";
import OtherSections from "@/components/ux/home/<USER>";
import { FounderSection } from "@/components/ux/home/<USER>";
import {
  getAllSeyNaturelleProducts,
  getImageComponentBySlug,
} from "@/lib/sanity/lib/actions";
import { imageComponent, seyNaturelleProductdType } from "@/types/types";
import aL from "@/public/images/pc-l.png";
import Image from "next/image";
import { Noise } from "@/components/ux/animations/noise";
import { getCVData } from "@/lib/cv-content";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import { ProgressiveBlur } from "@/components/ux/animations/progressive-blur";
import { FounderProfile } from "@/components/ux/about/founder-profile";

export const revalidate = 30;

export default async function Home() {
  // const imageComponent: imageComponent =
  //   await getImageComponentBySlug("home-carousel");

  // const seyNaturelleProducts: seyNaturelleProductdType[] =
  //   await getAllSeyNaturelleProducts();
  const cv = getCVData();
  return (
    <main>
      <section className="relative z-10 flex h-[44rem] w-full flex-col items-center justify-center overflow-hidden">
        <Noise />
        <Image
          alt="home"
          src={aL}
          className="absolute inset-0 z-0 h-[44rem] object-cover object-[80%] md:object-left"
        />
        <ProgressiveBlur
          className="absolute inset-0 bg-black/70"
          direction="bottom"
          blurLayers={6}
          blurIntensity={0.5}
        />
        <div className="relative z-50 flex h-[44rem] w-full max-w-6xl flex-col justify-end p-4">
          <div className="relative flex max-w-md flex-col space-y-2 py-4">
            <h1
              className={cn(
                aldineBT.className,
                "text-4xl text-white md:text-6xl",
              )}
            >
              {cv.personalInfo.name}
            </h1>
            <p className={cn("text-white/80")}>
              Licensed Aesthetician specializing in Medical Aesthetics,
              Semi-Permanent Makeup (SPMU), and Cosmetic Science. Expert trainer
              and facilitator with proven project management skills and
              multilingual capabilities (English, Twi, Fante, Ga).
            </p>
            <div className="pt-4">
              <Link
                href={"/contact"}
                className={cn(
                  buttonVariants({
                    size: "withIconRight",
                  }),
                  "group space-x-2 font-bold",
                )}
              >
                <span> Book A Consultation</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                </div>
              </Link>
            </div>
          </div>
        </div>
      </section>
      <FounderProfile />
    </main>
  );
}
