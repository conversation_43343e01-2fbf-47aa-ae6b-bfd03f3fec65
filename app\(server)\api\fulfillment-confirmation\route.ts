import FulfillmentConfirmationEmail from "@/emails/fulfilled-confirmation";
import { Database } from "@/types/supabase";
import { NextResponse } from "next/server";
import { Resend } from "resend";

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

type data = Database["public"]["Tables"]["store_backend"]["Row"];

export async function POST(request: Request) {
  const Data: data = await request.json();
  const email = Data.email!;
  try {
    const client = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      to: [email],
      subject: `Order with Ref:${Data.reference} Fulfilled`,
      react: FulfillmentConfirmationEmail(Data),
    });
    console.log("end");

    return NextResponse.json({ client });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
