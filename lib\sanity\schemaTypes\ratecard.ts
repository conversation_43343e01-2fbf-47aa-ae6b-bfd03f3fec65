//@ts-nocheck
import { defineField, defineType } from 'sanity'
import { WalletIcon } from 'lucide-react'

export default defineType({
 name: "ratecard",
 title: "Rate Card",
 icon: WalletIcon,
 type: 'document',
 fields: [
  defineField({
   name: 'orderid',
   title: 'Order ID',
   type: 'number',
  }),
  defineField({
   name: "packageName",
   title: "Package Name",
   type: 'string',
  }),
  defineField({
   name: 'slug',
   title: 'Slug',
   description: 'Always genereate to get the slug from the package name',
   type: 'slug',
   options: {
    source: 'packageName',
    maxLength: 112,
   },
   validation: (Rule) => Rule.required(),
  }),
  defineField({
   name: 'ratetype',
   title: 'Rate Type',
   type: 'string',
   options: {
    list:
     [
      { title: 'Package Rate', value: 'package' },
      { title: 'Single Rate', value: 'single' }
     ]
    ,
    layout: 'radio',
   },
   initialValue: 'package',
  }),
  defineField({
   name: "price",
   title: "Price",
   description: "The Price For The Package",
   type: "number",
  }),
  defineField({
   name: 'currency',
   title: 'Currency',
   description: "The currency of the Price For The Package",
   type: 'string',
   // validation: Rule => Rule.required().custom(value => ['gh', 'usa'].includes(value)),
   options: {
    list: [
     { title: 'Ghana', value: 'gh' },
     { title: 'United States', value: 'usa' },
    ],
    layout: 'radio', // This will render radio buttons for the options
   },
   initialValue: 'gh',
  }),
  defineField({
   name: "coverImage",
   title: "Cover Image",
   description: "The Image Cover For The Ratecard ",
   type: "image",
   options: {
    hotspot: true,
   },
   fields: [
    {
     name: 'alt',
     type: 'string',
     title: 'Alternative text',
     initialValue: 'img',
     validation: (Rule) => Rule.required(),
    },
   ],
  }),
  {
   name: "features",
   title: "Features",
   description: "The Features For The Ratecard",
   type: "array",
   of: [{ type: "block" }],
  },
 ],
 preview: {
  select: {
   title: 'packageName',
   orderid: 'orderid',
   ratetype: 'ratetype',
   price: 'price',
   media: "coverImage"
  },
  prepare(selection) {
   const { orderid, ratetype, price } = selection
   return { ...selection, subtitle: orderid && `${ratetype} | GHS${price}` }
  },
 },
})
