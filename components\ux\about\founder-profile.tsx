"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { FONT_PLAYFAIR_DISPLAY, aldineBT } from "@/constants/fonts";
import Image from "next/image";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";
import aboutImg from "@/public/images/bc.jpg";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const container = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export function FounderProfile() {
  return (
    <section className="w-full py-16">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={container}
          className="grid items-center gap-12 md:grid-cols-2"
        >
          {/* Left side - Profile Image and Quick Stats */}
          <motion.div variants={fadeInUp} className="space-y-6">
            <div className="relative">
              <div className="aspect-[3/4] w-full overflow-hidden rounded-2xl">
                {/* Replace with actual profile image path */}
                <div className="absolute inset-0 bg-black/10" />
                <Image
                  src={aboutImg}
                  alt="Linda Mensah - Founder of Blackcherry"
                  width={600}
                  height={800}
                  className="h-full w-full object-cover"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 pt-6">
              <div className="rounded-lg bg-gray-50 p-4 text-center">
                <p className="text-3xl font-bold">10+</p>
                <p className="text-sm text-gray-600">Years Experience</p>
              </div>
              <div className="rounded-lg bg-gray-50 p-4 text-center">
                <p className="text-3xl font-bold">1000+</p>
                <p className="text-sm text-gray-600">Happy Clients</p>
              </div>
            </div>
          </motion.div>

          {/* Right side - Content */}
          <motion.div variants={fadeInUp} className="space-y-8">
            <div>
              <h2
                className={cn(
                  FONT_PLAYFAIR_DISPLAY.className,
                  "mb-4 text-3xl font-medium",
                )}
              >
                Meet Linda Mensah
              </h2>
              <h3
                className={cn(aldineBT.className, "mb-6 text-xl text-gray-600")}
              >
                Founder & Lead Beauty Consultant
              </h3>
              <div className="space-y-4 text-gray-700">
                <p>
                  Linda Mensah is the visionary founder of Blackcherry, a
                  renowned beauty and wellness brand specializing in natural and
                  glamorous makeup & skincare services for high melanin ladies.
                </p>
                <p>
                  With a passion for natural beauty and a commitment to
                  chemical-free products, Linda has built Blackcherry into a
                  comprehensive beauty destination that encompasses professional
                  services, education, and wellness.
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className={cn(aldineBT.className, "text-xl font-medium")}>
                Our Divisions
              </h4>
              <ul className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">BC 360 Aesthetic Clinic</h5>
                  <p className="text-sm text-gray-600">
                    Specialized skincare services and natural remedies
                  </p>
                </li>
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">Sey Naturelle</h5>
                  <p className="text-sm text-gray-600">
                    Chemical-free cosmetic line with Ayurvedic herbs
                  </p>
                </li>
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">Beauty Academy</h5>
                  <p className="text-sm text-gray-600">
                    Professional beauty education and training
                  </p>
                </li>
                <li className="rounded-lg bg-gray-50 p-4">
                  <h5 className="mb-2 font-medium">Bridal Services</h5>
                  <p className="text-sm text-gray-600">
                    Specialized makeup and beauty services
                  </p>
                </li>
              </ul>
            </div>

            <div className="flex flex-wrap gap-4">
              <Link
                href="/contact"
                className={cn(
                  buttonVariants({
                    variant: "black",
                    size: "withIconRight",
                  }),
                  "group space-x-2",
                )}
              >
                <span>Book a Consultation</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-white" />
                </div>
              </Link>
              <Link
                href="/bridal-bookings-ratecard"
                className={cn(
                  buttonVariants({
                    variant: "outline",
                    size: "withIconRight",
                  }),
                  "group space-x-2",
                )}
              >
                <span>View Rate Card</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                </div>
              </Link>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
