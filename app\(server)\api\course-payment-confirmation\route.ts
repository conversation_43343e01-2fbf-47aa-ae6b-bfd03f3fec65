import {
  RegistrationConfirmationEmail,
  RegistrationConfirmationEmailOwner,
} from "@/emails/course-payment-confirmation";
import { coursePaymentDataType } from "@/types/types";
import { NextResponse } from "next/server";
import { Resend } from "resend";

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const data: coursePaymentDataType = await request.json();
  console.log("start");
  try {
    const owner = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      // to: ["<EMAIL>"],
      to: ["<EMAIL>"],
      bcc: ["<EMAIL>"],
      subject: "New Registration For Crash Course",
      text: "",
      react: RegistrationConfirmationEmailOwner(data),
    });

    const client = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      to: [data.payee.email],
      // bcc: ['iamju<PERSON><PERSON><EMAIL>'],
      subject: "Registration For Crash Course",
      text: "",
      react: RegistrationConfirmationEmail(data),
    });
    console.log("end");

    return NextResponse.json({ owner, client });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
