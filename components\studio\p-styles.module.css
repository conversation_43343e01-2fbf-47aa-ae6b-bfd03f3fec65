@reference "../../app/globals.css";

.portableText {
  @apply flex h-auto w-auto flex-col text-base;
}

.portableText ::selection {
  @apply bg-black;
}
/* .portableText h1 {
  @apply mb-2 text-xl text-alt-100;
} */
.portableText h2 {
  @apply mb-1 mt-2 text-base font-medium;
}

.portableText p {
  @apply mb-2 mt-1 text-gray-500;
}

.portableText li {
  @apply font-medium;
}

.portableText li::marker {
  @apply text-base;
}

.portableText strong {
  @apply text-white;
}

.portableText blockquote {
  @apply mb-1 mt-2 border-l-2 pl-2 ml-2 capitalize;
}

.portableText ul {
  @apply pl-6 pt-4;
}

.portableText ul > li {
  @apply list-disc;
}

.portableText ol {
  @apply pl-6 pt-4;
}

.portableText ol > li {
  @apply list-decimal;
}

.portableText a {
  @apply text-red-500 underline hover:text-red-500 transition-all duration-300 ease-linear;
}
