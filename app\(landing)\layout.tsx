import Footer from "@/components/footer";
import Navbar from "@/components/layouts/navbar";
import type { Metadata } from "next";

export const revalidate = 30;

export const metadata: Metadata = {
  metadataBase: new URL("https://www.blackcherrygh.com/"),
  title: {
    template: "%s | Black Cherry",
    default: "Black Cherry",
  },
  description:
    "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
  openGraph: {
    title: "Black Cherry",
    description:
      "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
    url: "https://www.blackcherrygh.com/",
    siteName: "Black Cherry",
    locale: "en-US",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
    nocache: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  icons: {
    shortcut: "/favicon-16x16.png",
    apple: "/public/apple-touch-icon.png",
    other: {
      rel: "apple-touch-icon-precomposed",
      url: "/public/android-chrome-192x192.png",
    },
  },
  twitter: {
    card: "summary_large_image",
    title: "Black Cherry",
    description:
      "Cosmetic Consultancy, Beauty Services, Training Services on Cosmetic.",
    site: "https://www.blackcherrygh.com/",
  },
};
export default function LandingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div>
      <Navbar />
      {children}
      <Footer />
    </div>
  );
}
