import localFont from "next/font/local";

import { Playfair_Display, Lato } from "next/font/google";

export const FONT_PLAYFAIR_DISPLAY = Playfair_Display({ subsets: ["latin"] });
export const FONT_LATO = Lato({
  subsets: ["latin"],
  weight: ["400", "700"],
});

export const fibonSans = localFont({
  src: [
    {
      path: "../public/Fonts/FibonSans-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../public/Fonts/FibonSans-Bold.woff2",
      weight: "900",
      style: "bold",
    },
  ],
});
export const aldineBT = localFont({
  src: [
    {
      path: "../public/Fonts/Aldine-721-BT-400.woff2",
      weight: "400",
      style: "normal",
    },
  ],
});

export const PolySansBlack = localFont({
  src: "../public/Fonts/PolySans-Bulky.woff2",
  weight: "900",
  style: "black",
});
