"use client";
import { galleryCategoresType } from "@/types/types";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

type Props = {
  categories: galleryCategoresType[];
};
export default function GalleryPageSwitchToggle({ categories }: Props) {
  const pathname = usePathname();
  return (
    <div className='flex flex-col items-center w-full'>
      <div className='flex flex-col max-w-4xl w-full items-center'>
        <div className='flex w-fit items-center border-b-2'>
          {categories.map((c, i) => (
            <div key={i} className='relative'>
              {pathname === `/gallery/${c.slug}` && (
                <motion.div
                  animate={{
                    scale: 1.03,
                    transition: { bounce: 1, duration: 0.2, type: "spring" },
                  }}
                  layoutId='gallery-switch-toggle'
                  className='h-0.5 inset-x-0 absolute bottom-0 bg-black'
                />
              )}
              <Link
                href={`/gallery/${c.slug}`}
                className='px-4 py-2 inline-flex'
              >
                <span
                  className={cn(
                    pathname === `/gallery/${c.slug}`
                      ? "text-black"
                      : "text-gray-500",
                    "font-medium transition-colors duration-300"
                  )}
                >
                  {c.imageName}
                </span>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
