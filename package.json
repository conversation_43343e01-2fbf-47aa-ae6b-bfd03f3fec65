{"name": "blackcherry", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "gen_types": "npx supabase gen types typescript --db-url postgres://postgres.ndaacfwbggkjxtjwacrh:<EMAIL>:6543/postgres?connect_timeout=5000 > types/supabase.ts"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@motionone/utils": "^10.18.0", "@portabletext/react": "^3.2.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@react-email/components": "^0.0.36", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.84.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-autoplay": "8.6.0", "embla-carousel-react": "8.6.0", "framer-motion": "^12.6.5", "groq": "^3.84.0", "lucide-react": "^0.488.0", "motion": "^12.7.3", "next": "15.3.0", "next-sanity": "^9.10.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-aria-components": "^1.8.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-paystack": "^6.0.0", "react-player": "^2.16.0", "resend": "^4.2.0", "sanity": "^3.84.0", "sharp": "^0.34.1", "sonner": "^2.0.3", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "vaul": "^1.1.2", "zod": "^4.0.0-beta.20250412T085909", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.3", "@types/node": "^22.14.1", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "eslint": "^9.24.0", "eslint-config-next": "15.3.0", "postcss": "^8.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.3", "typescript": "^5.8.3"}}