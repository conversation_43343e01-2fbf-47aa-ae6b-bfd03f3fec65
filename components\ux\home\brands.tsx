"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { aldineBT, FONT_PLAYFAIR_DISPLAY } from "@/constants/fonts";
import {
  brands_black_serete,
  brands_charter_house,
  brands_gh_one,
  brands_gtp,
  brands_mac,
  brands_max_beauty,
  brands_most_beautiful,
  brands_rozay_broklyn,
  brands_tv_3,
} from "@/constants/images";

const brandImages = [
  { id: 1, src: brands_gh_one, alt: "GH One" },
  { id: 2, src: brands_tv_3, alt: "TV3" },
  { id: 3, src: brands_rozay_broklyn, alt: "Rozay Brooklyn" },
  { id: 4, src: brands_max_beauty, alt: "Max Beauty" },
  { id: 5, src: brands_charter_house, alt: "Charter House" },
  { id: 6, src: brands_black_serete, alt: "Black Secret" },
  { id: 7, src: brands_gtp, alt: "GTP" },
  { id: 8, src: brands_most_beautiful, alt: "Most Beautiful" },
  { id: 9, src: brands_mac, alt: "MAC" },
];

export function Brands() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Calculate how many groups of 3 we have
  const totalGroups = Math.ceil(brandImages.length / 3);

  // Get the current group of brands to display
  const getCurrentBrands = () => {
    const startIndex = currentIndex * 3;
    return brandImages.slice(startIndex, startIndex + 3);
  };

  // Automatically change brands every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % totalGroups);
        setIsAnimating(false);
      }, 500); // Wait for exit animation to complete
    }, 5000);

    return () => clearInterval(interval);
  }, [totalGroups]);

  return (
    <main className="mx-auto w-full max-w-7xl">
      <div className="flex flex-col items-center justify-center space-y-8">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className={cn(
            FONT_PLAYFAIR_DISPLAY.className,
            "relative pb-4 text-3xl font-bold md:text-4xl",
          )}
        >
          <span className="relative z-10">Trusted By</span>
        </motion.h2>

        <div className="relative h-14 w-full max-w-sm overflow-hidden px-4">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              className="grid w-full grid-cols-3 gap-8"
            >
              {getCurrentBrands().map((brand, index) => (
                <motion.div
                  key={brand.id}
                  className="flex items-center justify-center"
                  initial={{ opacity: 0, filter: "blur(10px)", y: 20 }}
                  animate={{
                    opacity: 1,
                    filter: "blur(0px)",
                    y: 0,
                  }}
                  exit={{ opacity: 0, filter: "blur(10px)", y: -20 }}
                  transition={{
                    duration: 0.5,
                    delay: index * 0.15, // Staggered animation
                    ease: "easeInOut",
                  }}
                >
                  <div className="relative h-8 w-12 overflow-hidden">
                    <Image
                      src={brand.src}
                      alt={brand.alt}
                      fill
                      className="object-contain transition-all duration-300 hover:scale-110"
                    />
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mx-auto flex max-w-lg flex-col items-center p-4 pb-8 text-center md:max-w-3xl"
        >
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="leading-relaxed"
          >
            Blackcherry is well known for providing very natural and Glamorous
            makeup & skincare services for high melanin ladies using healthy and
            non-chemical-based cosmetic products, Our clients are privileged to
            enjoy all-round care in beauty from our services, product line, and
            educational programs.
          </motion.p>
        </motion.div>
      </div>
    </main>
  );
}
