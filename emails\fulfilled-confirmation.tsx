import { EmailImageComponent } from "@/components/studio/img";
import { currency } from "@/hooks/use-currency";
import { Database } from "@/types/supabase";
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
} from "@react-email/components";

type Data = Database["public"]["Tables"]["store_backend"]["Row"];

const websiteUrl = "https://www.blackcherrygh.com";
const logoUrl = "https://www.blackcherrygh.com/images/logo.png";

export const FulfillmentConfirmationEmail = (data: Data) => {
  return (
    <Html>
      <Head />
      <Preview>Your Order Has Been Fulfilled!</Preview>
      <Body style={main}>
        <Container style={container}>
          <Img
            src={logoUrl}
            style={{
              marginTop: "16px",
            }}
            width="82"
            height="65"
            alt="Logo"
          />
          <Heading style={h1}>Your Order Has Been Fulfilled!</Heading>
          <Text
            style={{
              ...text,
              marginBottom: "14px",
              fontWeight: "600",
              color: "gray",
            }}
          >
            <span style={{ color: "black" }}>
              Dear: {data.first_name} {data.last_name},
            </span>
            <br />
            <span style={{ paddingBottom: "14px" }}>
              We&apos;re thrilled to inform you that your order <br /> with ID:{" "}
              <span style={{ ...code, color: "black" }}>{data.reference}</span>{" "}
              has been successfully fulfilled! 🎉
            </span>
          </Text>
          <Text
            style={{
              ...text,
              marginBottom: "14px",
              fontWeight: "600",
              color: "gray",
            }}
          >
            <span style={{ color: "black" }}>Package Name</span>
            <br />
            <span>
              {data.products?.map((p) => {
                return `${p.packageName} (${currency(p.price, "GHS")})`;
              })}
            </span>
            <br />
            <span style={{ color: "black", paddingBottom: "14px" }}>
              Delivery Address
            </span>
            <br />
            <span>
              {data.street_name}, {data.city}, {data.region}
            </span>
            <br />
          </Text>
          <Text
            style={{
              ...text,
              marginBottom: "14px",
              fontWeight: "600",
              color: "gray",
            }}
          >
            Your package, filled with carefully curated products,
            <br /> has been lovingly prepared and dispatched. <br /> You can
            expect it to arrive at your doorstep soon.
            <br />
          </Text>
          <Text style={footer}>
            <Link
              href="https://www.blackcherrygh.com/"
              target="_blank"
              style={{ ...link, color: "black", fontWeight: "600" }}
            >
              Black Cherry
            </Link>
            <br />
            COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
            COSMETIC
            <br />
            Please contact us if you have any questions. <br /> (You can send
            your questions to our{" "}
            <Link
              href="https://www.blackcherrygh.com/contact"
              target="_blank"
              style={{ ...link, color: "#898989" }}
            >
              support page
            </Link>{" "}
            .)
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default FulfillmentConfirmationEmail;

const main = {
  backgroundColor: "#ffffff",
};

const container = {
  paddingLeft: "12px",
  paddingRight: "12px",
  margin: "0 auto",
};

const paragraph = {
  margin: "0",
  lineHeight: "2",
};

const adressTitle = {
  ...paragraph,
  fontSize: "15px",
  fontWeight: "bold",
};

const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "0",
  padding: "0",
};

const link = {
  color: "#2754C5",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  textDecoration: "underline",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "24px 0",
};

const footer = {
  color: "#898989",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "12px",
  lineHeight: "22px",
  marginTop: "12px",
  marginBottom: "24px",
};

const code = {
  display: "inline-block",
  padding: "4px 12px",
  width: "fit",
  backgroundColor: "#f4f4f4",
  borderRadius: "14px",
  border: "1px solid #eee",
  color: "#333",
};
