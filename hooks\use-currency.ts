type currency = "GHS" | "USD";

export function currency(price: number, currency: currency) {
  if (currency === "GHS") {
    const amountingh = new Intl.NumberFormat("en-GH", {
      style: "currency",
      currency: "GHS",
    }).format(price);

    return amountingh;
  } else if (currency === "USD") {
    const amountinusa = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price);

    return amountinusa;
  }
}
