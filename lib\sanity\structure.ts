import {
  BookImage,
  FileQuestionIcon,
  FileText,
  Image as ImageIcon,
  Layers,
  Leaf,
  Settings,
  ShieldAlert,
  ShoppingBasket,
  Sprout,
  SquareUser,
  Tag,
  WalletIcon,
} from "lucide-react";
import { StructureBuilder } from "sanity/desk";

export const structure = (S: StructureBuilder) =>
  S.list()
    .title("Content")
    .items([
      // E-commerce products
      S.listItem()
        .title("Store Products")
        .icon(ShoppingBasket)
        .child(S.documentTypeList("storeproducts").title("Store Products")),
      S.listItem()
        .title("Sey Naturelle Products")
        .icon(ShoppingBasket)
        .child(
          S.documentTypeList("seynaturelleproducts").title(
            "Sey Naturelle Products",
          ),
        ),
      S.listItem()
        .title("i-adore Products")
        .icon(ShoppingBasket)
        .child(S.documentTypeList("iadoreproducts").title("i-adore Products")),
      S.divider(),
      // Services and media
      S.listItem()
        .title("Rate Card")
        .icon(WalletIcon)
        .child(S.documentTypeList("ratecard").title("Rate Card")),
      S.listItem()
        .title("Gallery")
        .icon(BookImage)
        .child(S.documentTypeList("gallery").title("Gallery")),
      S.listItem()
        .title("Image Components")
        .icon(ImageIcon)
        .child(S.documentTypeList("imagecomponents").title("Image Components")),
      S.divider(),
      // Content pages
      S.listItem()
        .title("About")
        .icon(SquareUser)
        .child(S.documentTypeList("about").title("About")),
      S.listItem()
        .title("BC 360")
        .icon(Leaf)
        .child(S.documentTypeList("bc360content").title("BC 360")),
      S.listItem()
        .title("Sey Naturelle")
        .icon(Sprout)
        .child(
          S.documentTypeList("seynaturellecontent").title("Sey Naturelle"),
        ),
      S.divider(),
      // Legal and information
      S.listItem()
        .title("Terms & Conditions")
        .icon(FileText)
        .child(S.documentTypeList("terms").title("Terms & Conditions")),
      S.listItem()
        .title("FAQs")
        .icon(FileQuestionIcon)
        .child(S.documentTypeList("faqs").title("FAQs")),
      // New schema types for the e-commerce structure
      S.divider(),
      S.listItem()
        .title("New E-commerce")
        .icon(ShoppingBasket)
        .child(
          S.list()
            .title("E-commerce")
            .items([
              S.listItem()
                .title("Products")
                .icon(ShoppingBasket)
                .child(S.documentTypeList("product").title("Products")),
              S.listItem()
                .title("Categories")
                .icon(Tag)
                .child(S.documentTypeList("category").title("Categories")),
              S.listItem()
                .title("Sizes")
                .icon(Layers)
                .child(S.documentTypeList("sizes").title("Sizes")),
            ]),
        ),
      S.listItem()
        .title("Banners")
        .icon(ImageIcon)
        .child(S.documentTypeList("banner").title("Banners")),
      S.listItem()
        .title("Privacy Policy")
        .icon(ShieldAlert)
        .child(S.document().schemaType("privacy").documentId("privacy")),
      S.listItem()
        .title("General Settings")
        .icon(Settings)
        .child(S.document().schemaType("general").documentId("general")),
    ]);
