import { EmailImageComponent } from "@/components/studio/img";
import { currency, cn } from "@/lib/utils";
import { paymentData } from "@/types/types";
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
} from "@react-email/components";
import * as React from "react";

type Data = paymentData;
const websiteUrl = "https://www.blackcherrygh.com";
const logoUrl = "https://www.blackcherrygh.com/images/logo.png";

export const PaymentConfirmationEmailOwner = ({ data }: { data: Data }) => (
  <Html>
    <Preview>
      New Purchase For --
      {data.products.length > 1
        ? `${data.products[0].packageName} and more`
        : `${data.products[0].packageName}.`}
      ,{" "}
    </Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src={logoUrl}
          style={{
            marginTop: "16px",
          }}
          width="82"
          height="65"
          alt="Logo"
        />
        <Text style={{ ...text, marginBottom: "4px" }}>
          These are the details of the Purchase.
        </Text>
        <Text style={{ ...text, marginBottom: "14px" }}>
          <span style={{ color: "black", fontWeight: "700" }}>
            Payee Details
          </span>
          <br />
          Full Name : {data.payee.first_name} {data.payee.last_name} <br />
          Email : {data.payee.email} <br />
          Phone Number : {data.payee.phone_number} <br />
          Street Name : {data.payee.street_name} <br />
          City : {data.payee.city} <br />
          Region : {data.payee.region} <br />
        </Text>

        <Text style={{ ...text, marginBottom: "14px" }}>
          <span style={{ color: "black", fontWeight: "700" }}>
            Package Details
          </span>{" "}
          <br />
          {data.products.map((p, i) => {
            return (
              <span key={p._id}>
                [{i + 1}] - {p.packageName} -- {currency(p.price, "GHS")} [
                {p.quantity} Piece(s)]
                <br />
              </span>
            );
          })}
        </Text>
        <Text style={{ ...text, marginBottom: "14px" }}>
          Transaction No. : {data.reference.transaction} <br />
          Purchase Date : {data.date} <br />
          Total Product Amount : {currency(data.total_amount, "GHS")} <br />
        </Text>
        <Text style={footer}>
          <Link
            href="https://www.blackcherrygh.com/"
            target="_blank"
            style={{ ...link, color: "black", fontWeight: "600" }}
          >
            Black Cherry
          </Link>
          <br />
          COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
          COSMETIC
          <br />
          Please contact us if you have any questions. <br /> (You can send your
          questions to our{" "}
          <Link
            href="https://www.blackcherrygh.com/contact"
            target="_blank"
            style={{ ...link, color: "#898989" }}
          >
            support page
          </Link>{" "}
          .)
        </Text>
      </Container>
    </Body>
  </Html>
);
export const PaymentConfirmationEmail = ({ data }: { data: Data }) => (
  <Html>
    <Preview>
      Purchase Confirmation for --
      {data.products.length > 1
        ? `${data.products[0].packageName} and more`
        : `${data.products[0].packageName}.`}
      ,{" "}
    </Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src={logoUrl}
          style={{
            marginTop: "16px",
          }}
          width="82"
          height="65"
          alt="Logo"
        />
        <Text style={{ ...text, marginBottom: "14px" }}>
          Thank you for your purchase.
          <br />
          Shipping to: {data.payee.first_name} {data.payee.last_name},
          <br />
          {data.payee.street_name}, {data.payee.city}, {data.payee.region}
        </Text>

        <Text style={{ ...text, marginBottom: "14px" }}>
          <span style={{ color: "black", fontWeight: "700" }}>
            Package Details
          </span>{" "}
          <br />
          {data.products.map((p, i) => {
            return (
              <span key={p._id}>
                [{i + 1}] - {p.packageName} -- {currency(p.price, "GHS")} [
                {p.quantity} Piece(s)]
                <br />
              </span>
            );
          })}
        </Text>
        <Text style={{ ...text, marginBottom: "14px" }}>
          Transaction No. : {data.reference.transaction} <br />
          Purchase Date : {data.date} <br />
          Total Product Amount : {currency(data.total_amount, "GHS")} <br />
        </Text>
        <Text style={footer}>
          <Link
            href="https://www.blackcherrygh.com/"
            target="_blank"
            style={{ ...link, color: "black", fontWeight: "600" }}
          >
            Black Cherry
          </Link>
          <br />
          COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
          COSMETIC
          <br />
          Please contact us if you have any questions. <br /> (You can send your
          questions to our{" "}
          <Link
            href="https://www.blackcherrygh.com/contact"
            target="_blank"
            style={{ ...link, color: "#898989" }}
          >
            support page
          </Link>{" "}
          .)
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#ffffff",
};

const container = {
  paddingLeft: "12px",
  paddingRight: "12px",
  margin: "0 auto",
};

const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "0",
  padding: "0",
};

const link = {
  color: "#2754C5",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  textDecoration: "underline",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "24px 0",
};

const footer = {
  color: "#898989",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "12px",
  lineHeight: "22px",
  marginTop: "12px",
  marginBottom: "24px",
};

const code = {
  display: "inline-block",
  padding: "16px 4.5%",
  width: "90.5%",
  backgroundColor: "#f4f4f4",
  borderRadius: "5px",
  border: "1px solid #eee",
  color: "#333",
};
