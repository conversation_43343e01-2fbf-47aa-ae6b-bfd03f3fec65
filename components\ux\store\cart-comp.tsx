"use client";

import ImageComponent from "@/components/studio/img";
import { aldineBT } from "@/constants/fonts";
import { useCartStore } from "@/constants/store/cart";
import { cn } from "@/lib/utils";

import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { SelectNative } from "@/components/ui/select-native";
import useUuid from "@/hooks/useUuid";
import { supabaseClient } from "@/lib/supabase/client";
import { paymentData, paystactRefType } from "@/types/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { Minus, Plus, X } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import {
  Group,
  NumberField,
  Button as RAButton,
  Input as RAInput,
} from "react-aria-components";
import { useForm } from "react-hook-form";
import { PaystackConsumer } from "react-paystack";
import { toast } from "sonner";
import * as z from "zod";

// Add @ts-ignore to suppress module errors

// Define types for form fields
type FormField = {
  onChange: (value: any) => void;
  onBlur: () => void;
  value: any;
  name: string;
  ref: React.Ref<any>;
};

type PaystackInitializeFunction = (
  onSuccess: (ref: paystactRefType) => void,
  onClose: () => void,
) => void;
import { currency } from "@/hooks/use-currency";

const PAYSTACK_LIVE_API_KEY = process.env.NEXT_PUBLIC_PAYSTACK_LIVE_API_KEY!;
// const PAYSTACK_TEST_API_KEY = process.env.NEXT_PUBLIC_PAYSTACK_TEST_API_KEY!;

const regions = [
  "AHAFO",
  "ASHANTI",
  "BONO EAST",
  "BRONG AHAFO",
  "CENTRAL",
  "EASTERN",
  "GREATER ACCRA",
  "NORTH EAST",
  "NORTHERN",
  "OTI",
  "UPPER EAST",
  "UPPER WEST",
  "VOLTA",
  "WESTERN",
  "WESTERN NORTH",
];

export default function CartComp() {
  const paymentFormSchema = z.object({
    first_name: z.string().min(2, { error: "First Name is required." }),
    last_name: z.string().min(2, { error: "Last Name is required." }),
    email: z.email({ error: "Please enter a valid email address." }),
    phone_number: z.string().transform((data: string) => Number(data)),
    street_name: z.string().min(2, { error: "Street Name is required." }),
    city: z.string().min(2, { error: "City is required." }),
    region: z.enum(
      [
        "AHAFO",
        "ASHANTI",
        "BONO EAST",
        "BRONG AHAFO",
        "CENTRAL",
        "EASTERN",
        "GREATER ACCRA",
        "NORTH EAST",
        "NORTHERN",
        "OTI",
        "UPPER EAST",
        "UPPER WEST",
        "VOLTA",
        "WESTERN",
        "WESTERN NORTH",
      ],
      {
        error: "You need to select a Region.",
      },
    ),
  });

  const { cartItems, removeFromCart } = useCartStore((state) => state);

  const [products, setProducts] = useState(
    cartItems.map((c) => {
      return { ...c, quantity: 1 };
    }),
  );

  const productPrice = products.reduce((acc, item) => {
    return acc + item.price * item.quantity;
  }, 0);

  function incereaseProductQuantity(_id: string) {
    setProducts((prevProducts) =>
      prevProducts.map((product) =>
        product._id === _id
          ? { ...product, quantity: product.quantity + 1 }
          : product,
      ),
    );
  }

  function decreaseProductQuantity(_id: string) {
    setProducts((prevProducts) =>
      prevProducts.map((product) =>
        product._id === _id && product.quantity > 1
          ? { ...product, quantity: product.quantity - 1 }
          : product,
      ),
    );

    // Check if the quantity is 1 after decrementing
    const updatedProduct = products.find((product) => product._id === _id);
    if (updatedProduct && updatedProduct.quantity === 1) {
      // Remove the item from the cart store
      removeFromCart(_id);
    }
  }

  const handleRemoveProduct = (productId: string) => {
    // Remove the product from the Zustand store
    removeFromCart(productId);

    // Remove the product from the products array
    setProducts((prevProducts) =>
      prevProducts.filter((product) => product._id !== productId),
    );
  };

  const supabase = supabaseClient();
  const form = useForm<z.infer<typeof paymentFormSchema>>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      city: "",
      street_name: "",
      region: "GREATER ACCRA",
    },
  });

  const formData = form.watch();

  const date = new Date();
  const [SubmitBtn, setSubmitBtn] = useState("Make Payment");
  const [complete, setComplete] = useState(false);
  const paymentDate = format(date, "LLL d yyyy");
  const uuid = useUuid();
  const reference =
    formData.first_name.slice(0, 2) + formData.last_name.slice(0, 2) + uuid;

  async function onSubmit({ ref }: { ref: paystactRefType }) {
    return new Promise<paymentData>(async (resolve, reject) => {
      setSubmitBtn("Placing Order...");

      const data: paymentData = {
        products: products,
        payee: formData,
        reference: ref,
        date: paymentDate,
        total_amount: productPrice,
      };

      const { error } = await supabase.from("store_backend").insert([
        {
          first_name: data.payee.first_name,
          last_name: data.payee.last_name,
          email: data.payee.email,
          phone: data.payee.phone_number,
          street_name: data.payee.street_name,
          city: data.payee.city,
          region: data.payee.region,
          reference: data.reference.reference,
          transaction_id: data.reference.transaction,
          fulfilled: false,
          products: data.products,
        },
      ]);

      if (error) {
        reject(error.message);
        console.log(error);
      } else {
        await fetch("/api/store-payments", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });
        // toast.message(JSON.stringify(data, null, 2));

        setSubmitBtn("Payment Successful");
        resolve(data);
      }
    });
  }

  const config = {
    reference: reference,
    email: formData.email,
    firstname: formData.first_name,
    phone: formData.phone_number,
    amount: Number(productPrice + `00`), //Amount is in the country's lowest currency. E.g Kobo, so 20000 kobo = N200
    // publicKey: PAYSTACK_TEST_API_KEY, //Test Api Key
    publicKey: PAYSTACK_LIVE_API_KEY, //Live Api Key
  };

  function handleSuccess(ref: paystactRefType) {
    // Implementation for whatever you want to do with reference and after success call.
    //if the message is approved and the status is success submit the email and reference to the backend.

    if (ref.status === "success") {
      toast.promise(onSubmit({ ref: ref }), {
        loading: "Placing Order...",
        success: (data: paymentData) =>
          `Order Placed for ${data.payee.first_name}`,
        error: (err: Error) => `Error: ${err.message}`,
      });

      onSubmit({ ref: ref });
      setSubmitBtn("Payment Successful");
    } else {
      console.log("didnt work out :(");
      setSubmitBtn("Try Again :(");
    }

    console.log(ref);
  }

  // you can call this function anything
  const handleClose = () => {
    // implementation for  whatever you want to do when the Paystack dialog closed.
    console.log("closed");
    setSubmitBtn("Payment Canceled :(");
  };

  const componentProps = {
    ...config,
    text: "Make Payment",
    onSuccess: (ref: any) => handleSuccess(ref),
    onClose: handleClose,
  };

  useEffect(() => {
    if (
      formData.first_name.length > 0 &&
      formData.last_name.length > 0 &&
      formData.email.length > 0 &&
      formData.street_name.length > 0
    ) {
      setComplete(true);
    } else {
      setComplete(false);
    }
  }, [
    formData.email.length,
    formData.first_name.length,
    formData.last_name.length,
    formData.street_name.length,
  ]);

  const checkDisablilty = {
    disabled: !complete ? true : false,
  };
  return (
    <div className="mx-auto max-w-6xl">
      <div className="flex flex-col items-center justify-center">
        <h2 className={cn(aldineBT.className, "my-2 px-2 text-6xl font-bold")}>
          Store Cart
        </h2>
      </div>
      {products.length < 1 ? (
        <div className="mx-auto my-10 max-w-xl items-center space-y-2 text-center">
          <div>
            <h2 className="text-2xl font-medium">No Products in Cart</h2>
          </div>
          <div>
            <Link
              href={"/store"}
              className={cn(buttonVariants({ variant: "outline" }))}
            >
              Go Back To Store
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid p-4 md:grid-cols-5">
          <section className="bg-neutral-50 p-4 md:col-span-2">
            <div className="mb-4">
              <h2 className="text-2xl font-medium">Products</h2>
            </div>
            <div className="flex flex-col space-y-4">
              {products.map((c) => {
                return (
                  <div
                    key={c._id}
                    className="grid grid-cols-3 gap-2 rounded-2xl border border-neutral-200 p-2 hover:bg-neutral-100"
                  >
                    <div className="relative">
                      <div className="absolute top-1 left-1">
                        <Button
                          variant={"destructive"}
                          onClick={() => handleRemoveProduct(c._id)}
                          className="inline-flex h-6 w-6 rounded-full p-1"
                        >
                          <X className="size-4" />
                        </Button>
                      </div>
                      <ImageComponent
                        image={c.coverImage}
                        className="aspect-square rounded-xl border border-neutral-200 object-cover"
                      />
                    </div>
                    <div className="col-span-2 flex flex-col justify-end space-y-2">
                      <h2 className={cn(aldineBT.className, "text-xl")}>
                        {c.packageName}
                      </h2>
                      <p className="font-medium">{currency(c.price, "GHS")}</p>
                      <div>
                        <NumberField
                          value={c.quantity}
                          minValue={1}
                          maxValue={10}
                          onChange={(value) => {
                            if (value > c.quantity) {
                              incereaseProductQuantity(c._id);
                            } else {
                              decreaseProductQuantity(c._id);
                            }
                          }}
                        >
                          <Group className="border-input relative inline-flex h-8 w-28 items-center overflow-hidden rounded-md border text-sm">
                            <RAButton
                              slot="decrement"
                              className="-ms-px flex h-full cursor-pointer items-center justify-center rounded-s-md border-r border-neutral-200 bg-white px-2 hover:bg-neutral-100 disabled:cursor-not-allowed disabled:opacity-40"
                            >
                              <Minus size={16} />
                            </RAButton>
                            <RAInput
                              readOnly
                              className="bg-background w-full grow px-2 text-center outline-none"
                            />
                            <RAButton
                              slot="increment"
                              className="-me-px flex h-full cursor-pointer items-center justify-center rounded-e-md border-l border-neutral-200 bg-white px-2 hover:bg-neutral-100 disabled:cursor-not-allowed disabled:opacity-40"
                            >
                              <Plus size={16} />
                            </RAButton>
                          </Group>
                        </NumberField>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </section>
          <section className="p-4 md:col-span-2">
            <div className="sticky top-28 rounded-3xl border border-neutral-200 p-4">
              <div className="">
                <h2
                  className={cn(
                    aldineBT.className,
                    "my-2 px-2 text-4xl font-bold",
                  )}
                >
                  Make Payment
                </h2>
              </div>
              <div className="">
                <Form {...form}>
                  <div className="w-full space-y-6">
                    <div className="space-y-2 p-1">
                      <div className="flex w-full items-center justify-between space-x-2 text-sm text-gray-400">
                        <p>User Details</p>
                        <hr className="mt-0.5 w-full flex-1 bg-gray-400" />
                        <p>
                          <Plus className="h-4 w-4" />
                        </p>
                      </div>

                      <FormField
                        control={form.control}
                        name="first_name"
                        render={({ field }: { field: FormField }) => (
                          <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                              <Input placeholder="First Name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="last_name"
                        render={({ field }: { field: FormField }) => (
                          <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Last Name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phone_number"
                        render={({ field }: { field: FormField }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Phone Number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }: { field: FormField }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input placeholder="Email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* user address details */}

                    <div className="space-y-4 p-1">
                      <div className="flex w-full items-center justify-between space-x-2 text-sm text-gray-400">
                        <p>Delivery Address</p>
                        <hr className="mt-0.5 w-full flex-1 bg-gray-400" />
                        <p>
                          <Plus className="h-4 w-4" />
                        </p>
                      </div>
                      <FormField
                        control={form.control}
                        name="street_name"
                        render={({ field }: { field: FormField }) => (
                          <FormItem>
                            <FormLabel>Street Name | Address</FormLabel>
                            <FormControl>
                              <Input placeholder="Street Name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <div className="grid md:grid-cols-2 md:gap-2">
                        <FormField
                          control={form.control}
                          name="city"
                          render={({ field }: { field: FormField }) => (
                            <FormItem>
                              <FormLabel>City</FormLabel>
                              <FormControl>
                                <Input placeholder="City" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="region"
                          render={({ field }: { field: FormField }) => (
                            <FormItem>
                              <FormLabel>Region | Ghana</FormLabel>
                              <SelectNative
                                value={field.value}
                                onChange={field.onChange}
                                className="focus:border-bring-black mt-1 block w-full rounded-xl border border-gray-300 pr-10 pl-3 text-base focus:ring-black focus:outline-hidden sm:text-sm"
                              >
                                {regions.map((r, index) => (
                                  <option key={index} value={r}>
                                    {r}
                                  </option>
                                ))}
                              </SelectNative>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* user address details */}
                    <div className="flex w-full flex-col space-y-2">
                      <div className="flex w-full items-center justify-between space-x-2 text-sm text-gray-400">
                        <p>Payment</p>
                        <hr className="mt-0.5 w-full flex-1 bg-gray-400" />
                        <p>
                          <Plus className="h-4 w-4" />
                        </p>
                      </div>
                      <PaystackConsumer currency="GHS" {...componentProps}>
                        {/* @ts-ignore */}
                        {({
                          initializePayment,
                        }: {
                          initializePayment: PaystackInitializeFunction;
                        }) => (
                          <Button
                            variant={"outline"}
                            type="button"
                            onClick={() => {
                              initializePayment(handleSuccess, handleClose);
                            }}
                            className="items-center space-x-2"
                            {...checkDisablilty}
                          >
                            <span>{currency(productPrice, "GHS")}</span>
                            <span>{SubmitBtn}</span>
                          </Button>
                        )}
                      </PaystackConsumer>
                    </div>
                  </div>
                </Form>
              </div>
            </div>
          </section>
        </div>
      )}
    </div>
  );
}
