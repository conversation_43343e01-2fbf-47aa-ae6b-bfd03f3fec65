"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useState } from "react";

export const supportformSchema = z.object({
  first_name: z.string().min(2, {
    error: "first name must be at least 2 characters.",
  }),
  last_name: z.string().min(2, {
    error: "last name must be at least 2 characters.",
  }),
  email: z.email({
    error: "email is required.",
  }),
  phone: z
    .string()
    .min(7, {
      error: "number must be at least 7 characters.",
    })
    .max(14, {
      error: "number must be at most 14 characters.",
    })
    .transform((data) => Number(data)),
  package: z.enum(["studio", "outdoor", "wedding"], {
    error: "You need to select a notification type.",
  }),
  message: z.string().min(10, {
    error: "message must be at least 10 characters.",
  }),
});

export default function SupportForm() {
  const form = useForm<z.infer<typeof supportformSchema>>({
    resolver: zodResolver(supportformSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      package: "studio",
      message: "",
    },
  });

  const [submit, setSubmit] = useState("submit");

  async function onSubmit(values: z.infer<typeof supportformSchema>) {
    setSubmit("submitting");
    console.log(values);
    await fetch("/api/send", {
      method: "POST",
      headers: {
        "Content-Type": "application/json", // Fix typo: "Aplication/json" to "application/json"
      },
      body: JSON.stringify(values),
    });

    setSubmit("Message Sent 🎉");
  }

  return (
    <div className="">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-80 space-y-8">
          <FormField
            control={form.control}
            name="first_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>First Name</FormLabel>
                <FormControl>
                  <Input placeholder="Joe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="last_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Last Name</FormLabel>
                <FormControl>
                  <Input placeholder="Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="(*************" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="package"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Select A Package...</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    <FormItem className="flex items-center space-y-0 space-x-3">
                      <FormControl>
                        <RadioGroupItem value="studio" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Studio Session
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-y-0 space-x-3">
                      <FormControl>
                        <RadioGroupItem value="outdoor" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Outdoor Session
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-y-0 space-x-3">
                      <FormControl>
                        <RadioGroupItem value="wedding" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Wedding Session
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Message</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Tell us a little bit about yourself"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full">
            {submit}
          </Button>
        </form>
      </Form>
    </div>
  );
}
