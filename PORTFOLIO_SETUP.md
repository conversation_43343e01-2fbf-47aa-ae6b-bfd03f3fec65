# <PERSON> Portfolio Setup Guide

This guide will help you complete the setup of <PERSON>'s professional portfolio website.

## 📋 Current Status

✅ **Completed:**
- Portfolio website structure created
- Professional hero section with contact information
- About section with professional highlights
- Skills section with technical and professional capabilities
- Experience section with work history and projects
- Contact section with form and FAQ
- Responsive design and animations
- SEO metadata updated

🔄 **Needs Completion:**
- Extract actual CV content from PDF
- Add professional photo
- Update contact information
- Customize content to match <PERSON>'s actual experience

## 🚀 Next Steps

### 1. Extract CV Content from PDF

The PDF file "LINDA MENSAH CV 2.pdf" is in the project root. You need to:

1. **Extract the text content** from the PDF manually or using a PDF reader
2. **Update the CV data file** at `lib/cv-content.ts` with <PERSON>'s actual information
3. **Replace placeholder content** in `content/linda-mensah-cv.md`

### 2. Update Personal Information

In `lib/cv-content.ts`, update the `personalInfo` section:

```typescript
personalInfo: {
  name: "<PERSON>",
  email: "<EMAIL>", // Update with real email
  phone: "+233 XX XXX XXXX", // Update with real phone
  location: "Accra, Ghana", // Update if different
  linkedin: "linkedin.com/in/linda-mensah-actual", // Update with real LinkedIn
  portfolio: "lindamensah.com" // Update with actual domain
}
```

### 3. Add Professional Photo

1. **Add Linda's professional headshot** to `public/images/linda-mensah.jpg`
2. **Update the hero section** in `components/ux/portfolio/hero-section.tsx`:

Replace the placeholder photo section (around line 95) with:

```tsx
<Image
  src="/images/linda-mensah.jpg"
  alt="Linda Mensah"
  fill
  className="object-cover rounded-2xl"
  priority
/>
```

### 4. Update Work Experience

In `lib/cv-content.ts`, replace the placeholder work experience with Linda's actual roles:

```typescript
workExperience: [
  {
    title: "Actual Job Title",
    company: "Actual Company Name",
    duration: "2020 - Present",
    location: "Accra, Ghana",
    responsibilities: [
      "Actual responsibility 1",
      "Actual responsibility 2",
      "Actual responsibility 3"
    ]
  }
  // Add more positions...
]
```

### 5. Update Education

Replace placeholder education with Linda's actual qualifications:

```typescript
education: [
  {
    degree: "Actual Degree",
    institution: "Actual University",
    duration: "2016 - 2018",
    location: "Ghana",
    details: ["Actual achievements", "Relevant coursework"]
  }
]
```

### 6. Update Skills

Customize the skills sections with Linda's actual capabilities:

```typescript
skills: {
  technical: [
    "Actual Technical Skill 1",
    "Actual Technical Skill 2"
  ],
  professional: [
    "Actual Professional Skill 1",
    "Actual Professional Skill 2"
  ],
  languages: [
    { language: "English", proficiency: "Native" },
    { language: "Twi", proficiency: "Native" }
  ]
}
```

### 7. Add Certifications

Update with Linda's actual certifications:

```typescript
certifications: [
  {
    name: "Actual Certification Name",
    organization: "Actual Organization",
    year: "2021"
  }
]
```

### 8. Update Projects

Replace with Linda's actual projects and achievements:

```typescript
projects: [
  {
    title: "Actual Project Title",
    duration: "2022 - 2023",
    description: "Actual project description",
    outcomes: "Actual outcomes and impact",
    technologies: "Actual tools and technologies used"
  }
]
```

## 🎨 Customization Options

### Colors and Branding

The portfolio uses a professional blue and slate color scheme. To customize:

1. **Primary colors** are defined in the Tailwind classes:
   - `bg-blue-600` (primary blue)
   - `bg-slate-900` (dark text)
   - `bg-slate-50` (light background)

2. **To change the color scheme**, update the color classes throughout the components.

### Typography

The portfolio uses two fonts:
- **Aldine BT** for headings (imported from `@/constants/fonts`)
- **Fibon Sans** for body text

### Layout

Each section is modular and can be:
- Reordered by changing the order in `app/(landing)/page.tsx`
- Hidden by commenting out sections
- Customized by editing individual component files

## 📱 Testing

After updating the content:

1. **Run the development server**: `npm run dev`
2. **Check responsiveness** on different screen sizes
3. **Test contact form** functionality
4. **Verify all links** work correctly
5. **Check SEO metadata** in browser dev tools

## 🚀 Deployment

When ready to deploy:

1. **Update the domain** in `app/layout.tsx` metadata
2. **Add the actual domain** to the `metadataBase` URL
3. **Deploy to your preferred hosting platform** (Vercel, Netlify, etc.)

## 📞 Support

If you need help with any of these steps, the portfolio structure is designed to be easily customizable. Each component is well-documented and follows React/Next.js best practices.

---

**Note**: Remember to replace all placeholder content with Linda's actual information before going live!
