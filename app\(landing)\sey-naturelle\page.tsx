import { buttonVariants } from "@/components/ui/button";
import { sn_18, sn_21, sn_Logo } from "@/constants/images";
import { cn } from "@/lib/utils";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import Footer from "@/components/footer";
import Navbar from "@/components/layouts/navbar";
import { aldineBT } from "@/constants/fonts";
import { Metadata } from "next";
import Description, { PDescription } from "@/components/studio/post-body";
import { Suspense } from "react";
import ImageComponent from "@/components/studio/img";
import { getSeyNaturelleContent } from "@/lib/sanity/lib/actions";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "Sey Naturelle",
  description:
    "Chemical-free cosmetic line infused with Ayurvedic herbs for exceptional skin wellness.",
  openGraph: {
    title: "Sey Naturelle | Black Cherry",
    description:
      "Chemical-free cosmetic line infused with Ayurvedic herbs for exceptional skin wellness.",
    url: "https://www.blackcherrygh.com/sey-naturelle/",
    siteName: "Sey Naturelle | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sey Naturelle | Black Cherry",
    description:
      "Chemical-free cosmetic line infused with Ayurvedic herbs for exceptional skin wellness.",
    site: "https://www.blackcherrygh.com/sey-naturelle/",
  },
};

export default async function Page() {
  const data = await getSeyNaturelleContent();
  return (
    <main className="bg-gray-100">
      {/* hero section */}
      <section className="flex w-full flex-col items-center px-4 pt-20">
        <div className="relative z-10 mb-10 flex h-[40rem] w-full max-w-6xl flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
          <div className="relative">
            <div className="absolute inset-0 bg-black/70" />
            <Image
              alt="sey_naturelle"
              src={sn_21}
              className="h-[40rem] object-cover"
            />
          </div>
          <div className="absolute bottom-4 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
            <div className="flex flex-col items-center space-y-2 text-white">
              <div>
                <Image
                  alt="sey_naturelle_logo"
                  src={sn_Logo}
                  className="w-48"
                />
              </div>
              <h2 className="text-5xl">Sey Naturelle</h2>
              <Suspense fallback={<div>loading...</div>}>
                <p className="">{data.subtitle}</p>
              </Suspense>
              <div className="pt-4">
                <Link
                  href={"/store#sey-naturelle"}
                  className={cn(
                    buttonVariants({
                      variant: "default",
                      size: "withIconRight",
                    }),
                    "group space-x-2",
                  )}
                >
                  <span>View In Store</span>
                  <div className="flex items-center justify-center pl-2">
                    <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                    <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* tmain s=content */}
      <section className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-5xl flex-col items-center p-4">
          <div className="flex h-full w-full flex-col">
            {/* top */}
            <div className="grid gap-2 md:grid-cols-2">
              <div className="order-last mt-14 flex flex-col space-y-4 pb-4 md:order-first md:mt-0 md:pb-0">
                <div>
                  <PDescription content={data.descriptionone} />
                </div>
              </div>
              <div className="order-first -mb-10 h-auto overflow-hidden md:order-last md:h-[24rem]">
                <div className="-mt-72">
                  <ImageComponent image={data.imageone} />
                </div>
              </div>
            </div>
            {/* down */}
            <div className="grid gap-2 md:grid-cols-2">
              <div className="h-[22rem] overflow-hidden">
                <div className="-mt-40">
                  <ImageComponent image={data.imagetwo} />
                </div>
              </div>
              <div className="flex flex-col space-y-4 p-4 md:mt-14">
                <div>
                  <div>
                    <PDescription content={data.descriptiontwo} />
                  </div>
                </div>
                <div>
                  <Link
                    href={"/store#sey-naturelle"}
                    className={cn(
                      buttonVariants({
                        variant: "default",
                        size: "withIconRight",
                      }),
                      "group space-x-2 bg-black text-white hover:bg-gray-900",
                    )}
                  >
                    <span>Browse Full Collection</span>
                    <div className="flex items-center justify-center pl-2">
                      <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-white transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                      <ChevronRight className="h-5 w-5 text-white transition-all duration-300 ease-linear group-hover:text-white" />
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* last section */}
      <section className="flex flex-col items-center px-4 pt-10">
        <div className="relative z-10 mb-10 flex h-[28rem] w-full max-w-6xl flex-col items-center justify-center overflow-hidden rounded-3xl bg-gray-50">
          <div className="relative">
            <div className="absolute inset-0 bg-black/70" />
            <Image
              alt="sey_naturelle"
              src={sn_18}
              className="h-[40rem] object-cover"
            />
          </div>
          <div className="absolute bottom-2 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
            <div className="flex flex-col items-center space-y-2 text-white">
              <h2 className={cn(aldineBT.className, "mb-4 text-5xl")}>
                Explore Our Targeted Formulations
              </h2>
              <p>
                Our products have been formulated to target healing skin
                conditions such as Acne, Eczema, Hyperpigmentation, skin
                discolorations, undiagnosed dermatitis, etc.
              </p>
              <p>
                Speak to our aesthetician for Free Skin consultation and product
                recommendation,We would love to hear from you.
              </p>
              <div className="pt-4">
                <Link
                  href={"/contact"}
                  className={cn(
                    buttonVariants({
                      variant: "default",
                      size: "withIconRight",
                    }),
                    "group space-x-2",
                  )}
                >
                  <span>Contact Us</span>
                  <div className="flex items-center justify-center pl-2">
                    <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                    <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
