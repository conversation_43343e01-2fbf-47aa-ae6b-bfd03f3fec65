"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { aldineBT, fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { CVData } from "@/lib/cv-content";
import { motion } from "framer-motion";
import {
  Mail,
  Phone,
  MapPin,
  Linkedin,
  Send,
  MessageSquare,
  Clock,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface ContactSectionProps {
  cvData: CVData;
}

export default function ContactSection({ cvData }: ContactSectionProps) {
  const { personalInfo } = cvData;
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log("Form submitted:", formData);
  };

  const contactMethods = [
    {
      icon: Mail,
      title: "Email",
      value: personalInfo.email,
      href: `mailto:${personalInfo.email}`,
      description: "Send me an email anytime",
    },
    {
      icon: Phone,
      title: "Phone",
      value: personalInfo.phone,
      href: `tel:${personalInfo.phone}`,
      description: "Call for immediate response",
    },
    {
      icon: Linkedin,
      title: "LinkedIn",
      value: "Connect with me",
      href: `https://${personalInfo.linkedin}`,
      description: "Professional networking",
    },
    {
      icon: MapPin,
      title: "Location",
      value: personalInfo.location,
      href: "#",
      description: "Based in Ghana",
    },
  ];

  const faqs = [
    {
      question: "What beauty services do you offer?",
      answer:
        "I offer medical aesthetics, semi-permanent makeup (SPMU), cosmetic science consultations, makeup and lash services, and professional beauty training. I'm a licensed aesthetician specializing in Botox, chemical peels, laser treatments, and fillers.",
    },
    {
      question: "Do you provide beauty training and education?",
      answer:
        "Yes! I'm a certified TVET trainer and have been delivering professional makeup and beauty training through BlackCherry Gh since 2016. I offer courses in cosmetology, makeup artistry, and advanced beauty techniques.",
    },
    {
      question: "What are your qualifications and certifications?",
      answer:
        "I'm a licensed Aesthetician by the North American Board of Aesthetic Medicine, with an Advanced Diploma in Cosmetology and a Master's in Innovation Management and Leadership. I have multiple certifications in cosmetic science, chemical peels, and beauty therapy.",
    },
    {
      question: "Can you provide beauty consulting for brands?",
      answer:
        "Absolutely! I have extensive experience as a beauty consultant, having worked with brands like Note Cosmetics, Colleen Makeup, and Max Beauty. I offer product development, brand strategy, and training services for beauty businesses.",
    },
  ];

  return (
    <section id="contact" className="bg-slate-50 py-20">
      <div className="mx-auto max-w-7xl px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-16 text-center"
        >
          <h2
            className={cn(
              aldineBT.className,
              "mb-6 text-4xl font-bold text-slate-900 lg:text-5xl",
            )}
          >
            Let's Work Together
          </h2>
          <div className="mx-auto mb-8 h-1 w-24 bg-blue-600" />
          <p
            className={cn(
              fibonSans.className,
              "mx-auto max-w-3xl text-xl leading-relaxed text-slate-600",
            )}
          >
            Ready to enhance your beauty journey or explore training
            opportunities? I'd love to hear from you and discuss how BlackCherry
            can help you achieve your beauty goals.
          </p>
        </motion.div>

        <div className="grid gap-16 lg:grid-cols-2">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="rounded-2xl bg-white p-8 shadow-lg"
          >
            <div className="mb-6 flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                <MessageSquare className="h-5 w-5 text-white" />
              </div>
              <h3
                className={cn(
                  aldineBT.className,
                  "text-2xl font-bold text-slate-900",
                )}
              >
                Send a Message
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label
                    className={cn(
                      fibonSans.className,
                      "mb-2 block text-sm font-medium text-slate-700",
                    )}
                  >
                    Your Name
                  </label>
                  <Input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your name"
                    className="w-full"
                    required
                  />
                </div>
                <div>
                  <label
                    className={cn(
                      fibonSans.className,
                      "mb-2 block text-sm font-medium text-slate-700",
                    )}
                  >
                    Email Address
                  </label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email"
                    className="w-full"
                    required
                  />
                </div>
              </div>

              <div>
                <label
                  className={cn(
                    fibonSans.className,
                    "mb-2 block text-sm font-medium text-slate-700",
                  )}
                >
                  Subject
                </label>
                <Input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="What's this about?"
                  className="w-full"
                  required
                />
              </div>

              <div>
                <label
                  className={cn(
                    fibonSans.className,
                    "mb-2 block text-sm font-medium text-slate-700",
                  )}
                >
                  Message
                </label>
                <Textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Tell me about your project or inquiry..."
                  rows={6}
                  className="w-full"
                  required
                />
              </div>

              <Button
                type="submit"
                size="lg"
                className="w-full bg-blue-600 text-white hover:bg-blue-700"
              >
                <Send className="mr-2 h-4 w-4" />
                Send Message
              </Button>
            </form>
          </motion.div>

          {/* Contact Information & FAQs */}
          <div className="space-y-8">
            {/* Contact Methods */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="rounded-2xl bg-white p-8 shadow-lg"
            >
              <h3
                className={cn(
                  aldineBT.className,
                  "mb-6 text-2xl font-bold text-slate-900",
                )}
              >
                Get In Touch
              </h3>

              <div className="space-y-4">
                {contactMethods.map((method, index) => (
                  <motion.div
                    key={method.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Link
                      href={method.href}
                      className="group flex items-center space-x-4 rounded-lg p-4 transition-colors hover:bg-slate-50"
                    >
                      <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 transition-colors group-hover:bg-blue-600">
                        <method.icon className="h-5 w-5 text-blue-600 transition-colors group-hover:text-white" />
                      </div>
                      <div className="flex-1">
                        <h4
                          className={cn(
                            fibonSans.className,
                            "font-semibold text-slate-900",
                          )}
                        >
                          {method.title}
                        </h4>
                        <p
                          className={cn(
                            fibonSans.className,
                            "font-medium text-blue-600",
                          )}
                        >
                          {method.value}
                        </p>
                        <p
                          className={cn(
                            fibonSans.className,
                            "text-sm text-slate-600",
                          )}
                        >
                          {method.description}
                        </p>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Response Time */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="rounded-2xl bg-gradient-to-r from-blue-600 to-slate-700 p-6 text-white"
            >
              <div className="mb-4 flex items-center space-x-3">
                <Clock className="h-6 w-6" />
                <h4 className={cn(aldineBT.className, "text-xl font-bold")}>
                  Quick Response
                </h4>
              </div>
              <p className={cn(fibonSans.className, "text-blue-100")}>
                I typically respond to inquiries within 24 hours. For urgent
                matters, please call directly or mention "urgent" in your
                message subject.
              </p>
            </motion.div>
          </div>
        </div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <h3
            className={cn(
              aldineBT.className,
              "mb-12 text-center text-3xl font-bold text-slate-900",
            )}
          >
            Frequently Asked Questions
          </h3>

          <div className="grid gap-6 md:grid-cols-2">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="rounded-xl bg-white p-6 shadow-lg"
              >
                <div className="mb-3 flex items-start space-x-3">
                  <CheckCircle className="mt-1 h-5 w-5 flex-shrink-0 text-green-600" />
                  <h4
                    className={cn(
                      fibonSans.className,
                      "font-bold text-slate-900",
                    )}
                  >
                    {faq.question}
                  </h4>
                </div>
                <p
                  className={cn(
                    fibonSans.className,
                    "ml-8 leading-relaxed text-slate-600",
                  )}
                >
                  {faq.answer}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
