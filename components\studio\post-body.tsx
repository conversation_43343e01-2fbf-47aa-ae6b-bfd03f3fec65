"use cleint";
import { cn } from "@/lib/utils";
import { PortableText, PortableTextComponents } from "@portabletext/react";
import Image from "next/image";
import pTextStyles from "./p-styles.module.css";
import portableTextStyles from "./portable-text-styles.module.css";
import { urlFor } from "@/lib/sanity/lib/image";

type sanityImageType = {
  _type: string;
  alt: string;
  _key: string;
  asset: {
    _ref: string;
    _type: "reference";
  };
  dimensions: {
    _type: "sanity.imageDimensions";
    width: number;
    aspectRatio: number;
    height: number;
  };
};
const SanityImage = ({ asset, dimensions, alt }: sanityImageType) => {
  // console.log(dimensions);
  return (
    <div className="w-full rounded-xl border bg-gray-100 p-1 dark:border-gray-900 dark:bg-gray-900/50">
      <div className="flex w-full">
        <Image
          className="w-full rounded-md"
          width={dimensions ? dimensions.width : 200}
          height={dimensions ? dimensions.height : 100}
          alt="Blog_img"
          src={urlFor(asset)
            .height(dimensions ? dimensions.height : 1000)
            .width(dimensions ? dimensions.width : 2000)
            .url()}
          sizes="100vw"
        />
      </div>
      <div className="">
        <em className="px-2 text-xs font-light">
          {alt ? `Alt : ${alt}` : "image"}
        </em>
      </div>
    </div>
  );
};

const myPortableTextComponents: PortableTextComponents = {
  types: {
    image: ({ value }) => {
      return <SanityImage {...value} />;
    },
    // code: ({ value }) => {
    //   return (
    //     <div className='text-base'>
    //       <Code lang='tsx' className={`${SFMonoRegular.className}`}>
    //         {value.code}
    //       </Code>
    //     </div>
    //   );
    // },
  },
  block: {
    // Ex. 1: customizing common block types
    h1: ({ children }) => (
      <h1 className={`text-alt-100 mb-2 text-2xl`}>{children}</h1>
    ),
    h2: ({ children }) => (
      <h2 className={`text-alt-100 mb-2 text-xl`}>{children}</h2>
    ),
    h5: ({ children }) => (
      <div
        className={`text-alt-200 border-alt-300 mb-2 rounded-xl border-2 p-4 pt-1`}
      >
        <span className="text-alt-100">Notes : </span>
        <span className="">{children}</span>
      </div>
    ),
  },
  marks: {
    strong: ({ children }: { children: React.ReactNode }) => (
      <span className={`text-black`}>{children}</span>
    ),
  },
};

export default async function Description({
  content,
  className,
}: {
  content: any;
  className?: string;
}) {
  // console.log(content);
  return (
    <div
      className={cn(
        "flex w-full flex-col",
        portableTextStyles.portableText,
        className,
      )}
    >
      <PortableText value={content} components={myPortableTextComponents} />
    </div>
  );
}
export async function PDescription({
  content,
  className,
}: {
  content: any;
  className?: string;
}) {
  // console.log(content);
  return (
    <div
      className={cn(
        "flex w-full flex-col",
        pTextStyles.portableText,
        className,
      )}
    >
      <PortableText value={content} components={myPortableTextComponents} />
    </div>
  );
}
