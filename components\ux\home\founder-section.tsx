"use client";
import { aldineBT } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { ChevronRight } from "lucide-react";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const container = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

export function FounderSection() {
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={container}
      className="flex w-full flex-col items-center py-16"
    >
      <div className="flex w-full max-w-7xl flex-col items-center px-4">
        <motion.div variants={fadeInUp} className="mb-8 text-center">
          <h2 className={cn(aldineBT.classN<PERSON>, "mb-4 text-4xl font-medium")}>
            Meet <PERSON>
          </h2>
          <p className="mx-auto max-w-2xl text-lg">
            Founder of Blackcherry, a renowned beauty and wellness brand
            specializing in natural and glamorous makeup & skincare services for
            high melanin ladies.
          </p>
        </motion.div>
        <motion.div
          variants={fadeInUp}
          className="grid w-full max-w-4xl gap-8 md:grid-cols-2"
        >
          <div className="space-y-6">
            <h3 className={cn(aldineBT.className, "text-2xl font-medium")}>
              Our Vision
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>Promoting natural beauty and empowering women</span>
              </li>
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>Using chemical-free and healthy products</span>
              </li>
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>Providing all-round care in beauty</span>
              </li>
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>
                  Supporting local businesses and natural beauty enhancement
                </span>
              </li>
            </ul>
          </div>

          <div className="space-y-6">
            <h3 className={cn(aldineBT.className, "text-2xl font-medium")}>
              Our Services
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>Professional makeup and skincare services</span>
              </li>
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>BC 360 Aesthetic Clinic for specialized treatments</span>
              </li>
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>Sey Naturelle chemical-free product line</span>
              </li>
              <li className="flex items-start">
                <ChevronRight className="mt-1 mr-2 h-5 w-5 flex-shrink-0" />
                <span>Beauty Business Academy training programs</span>
              </li>
            </ul>
          </div>
        </motion.div>
        <motion.div variants={fadeInUp} className="mt-8">
          <Link
            href="/about"
            className={cn(
              buttonVariants({
                variant: "black",
                size: "withIconRight",
              }),
              "group space-x-2",
            )}
          >
            <span>Learn More About Us</span>
            <div className="flex items-center justify-center pl-2">
              <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
              <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-white" />
            </div>
          </Link>
        </motion.div>{" "}
      </div>
    </motion.div>
  );
}
