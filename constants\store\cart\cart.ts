import { seyNaturelleProductdType } from "@/types/types";
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type CartState = {
  cartItems: seyNaturelleProductdType[];
};

export type CartActions = {
  addToCart: (product: seyNaturelleProductdType) => void;
  removeFromCart: (productId: string) => void;
  clearCart: () => void;
};

export type CartStore = CartState & CartActions;

export const createCartStore = create<CartStore>()(
  persist(
    (set) => ({
      cartItems: [],
       addToCart: (product) =>
      set((state) => ({
        cartItems: [...state.cartItems, product],
      })),
    removeFromCart: (productId) =>
      set((state) => ({
        cartItems: state.cartItems.filter((item) => item._id !== productId),
      })),
    clearCart: () =>
      set({
        cartItems: [],
      }),
    }),
    {
      name: 'blackcherry-sey-naturelle-cart',
    }
  )
);
