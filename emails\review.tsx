import { formSchema } from "@/components/forms/reviews/review-form";
import { StarSolidIcon } from "@/components/ux/icons";
import { cn } from "@/lib/utils";
import { currency } from "@/hooks/use-currency";
import { Database } from "@/types/supabase";
import { coursePaymentDataType } from "@/types/types";
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
} from "@react-email/components";
import * as React from "react";
import { z } from "zod";

type Data = z.infer<typeof formSchema>;
const websiteUrl = "https://www.blackcherrygh.com";
const logoUrl = "https://www.blackcherrygh.com/images/logo.png";
export const ReviewEmailOwner = (data: Data) => (
  <Html>
    <Head />
    <Preview>{data.customer_name} Left A Review</Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src={logoUrl}
          style={{
            marginTop: "16px",
          }}
          width="82"
          height="65"
          alt="Logo"
        />
        <Heading style={h1}>{data.customer_name} Left A Review</Heading>
        <Text
          style={{
            ...text,
            marginBottom: "14px",
            fontWeight: "600",
            color: "gray",
          }}
        >
          <span style={{ color: "black", fontWeight: "700" }}>
            Here Are the details of the Review
          </span>
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>Full Name</span>
          <br /> {data.customer_name!} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Email</span>
          <br /> {data.customer_email} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Rating</span>
          <br />{" "}
          <div className="flex items-center justify-center space-x-1">
            {[...Array(5)].map((_, i) => {
              const ratingValue = i + 1;

              return (
                <StarSolidIcon
                  key={i}
                  style={{
                    color:
                      ratingValue <= data.rating! ? "yellow.400" : "gray.300",
                  }}
                />
              );
            })}
          </div>
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Rating Message
          </span>
          <br /> {data.message} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Rating Type</span>
          <br /> {data.review_type} <br />
        </Text>
        <Text style={footer}>
          <Link
            href={websiteUrl}
            target="_blank"
            style={{ ...link, color: "#898989" }}
          >
            Black Cherry
          </Link>
          <br />
          COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
          COSMETIC
        </Text>
      </Container>
    </Body>
  </Html>
);
export const ReviewEmail = (data: Data) => (
  <Html>
    <Head />
    <Preview>You Left A Review at Blackcherry</Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src={logoUrl}
          style={{
            marginTop: "16px",
          }}
          width="82"
          height="65"
          alt="Logo"
        />
        <Heading style={h1}>You Left A Review at Blackcherry</Heading>
        <Text
          style={{
            ...text,
            marginBottom: "14px",
            fontWeight: "600",
            color: "black",
          }}
        >
          <span>Hello {data.customer_name},</span>
          <br />
          <span>Your review has been submitted successfully.</span>
        </Text>
        <Text
          style={{
            ...text,
            marginBottom: "14px",
            fontWeight: "600",
            color: "gray",
          }}
        >
          <span style={{ color: "black", fontWeight: "700" }}>
            Here Are the details of the Review
          </span>
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>Full Name</span>
          <br /> {data.customer_name!} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Email</span>
          <br /> {data.customer_email} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Rating</span>
          <br />{" "}
          <div className="flex items-center justify-center space-x-1">
            {[...Array(5)].map((_, i) => {
              const ratingValue = i + 1;

              return (
                <StarSolidIcon
                  key={i}
                  style={{
                    color:
                      ratingValue <= data.rating! ? "yellow.400" : "gray.300",
                  }}
                />
              );
            })}
          </div>
          <br />
          <span style={{ color: "black", fontWeight: "700" }}>
            Rating Message
          </span>
          <br /> {data.message} <br />
          <span style={{ color: "black", fontWeight: "700" }}>Rating Type</span>
          <br /> {data.review_type} <br />
        </Text>
        <Text style={footer}>
          <Link
            href="https://www.blackcherrygh.com/"
            target="_blank"
            style={{ ...link, color: "black", fontWeight: "600" }}
          >
            Black Cherry
          </Link>
          <br />
          COSMETIC CONSULTANCY, BEAUTY SERVICES, <br /> TRAINING SERVICES ON
          COSMETIC
          <br />
          Please contact us if you have any questions. <br /> (You can send your
          questions to our{" "}
          <Link
            href="https://www.blackcherrygh.com/contact"
            target="_blank"
            style={{ ...link, color: "#898989" }}
          >
            support page
          </Link>{" "}
          .)
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#ffffff",
};

const container = {
  paddingLeft: "12px",
  paddingRight: "12px",
  margin: "0 auto",
};

const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "0",
  padding: "0",
};

const link = {
  color: "#2754C5",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  textDecoration: "underline",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "24px 0",
};

const footer = {
  color: "#898989",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "12px",
  lineHeight: "22px",
  marginTop: "12px",
  marginBottom: "24px",
};

const code = {
  display: "inline-block",
  padding: "16px 4.5%",
  width: "90.5%",
  backgroundColor: "#f4f4f4",
  borderRadius: "5px",
  border: "1px solid #eee",
  color: "#333",
};
