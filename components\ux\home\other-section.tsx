"use client";

import { buttonVariants } from "@/components/ui/button";
import { aldineBT } from "@/constants/fonts";
import { sn_Logo } from "@/constants/images";
import { cn } from "@/lib/utils";
import {
  Banknote,
  CalendarClock,
  CalendarRange,
  ChevronRight,
  FileBadge,
  FlaskRound,
  GraduationCap,
  Leaf,
  Map,
  NotebookTabs,
  Store,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import Testimonials from "./testimonials";
import { motion } from "framer-motion";

import ImageComponent from "@/components/studio/img";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselMinus,
  CarouselPlus,
} from "@/components/ui/carousel";
import { seyNaturelleProductdType } from "@/types/types";
import { currency } from "@/hooks/use-currency";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 60 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.5 } },
};

const slideInRight = {
  hidden: { opacity: 0, x: 100 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.5, ease: "easeOut" } },
};

export default function OtherSections({
  seyNaturelleProducts,
}: {
  seyNaturelleProducts: seyNaturelleProductdType[];
}) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      className="flex w-full flex-col items-center"
    >
      <motion.div
        variants={staggerContainer}
        initial="hidden"
        animate="visible"
        className="flex w-full max-w-7xl flex-col items-center"
      >
        <motion.div variants={scaleIn} className="h-full w-full">
          {/* sey naturelle */}
          <section className="px-4">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-100px" }}
              variants={fadeInUp}
              className="relative z-10 my-10 flex h-full w-full flex-col rounded-[2.5rem] border bg-gray-50"
            >
              <div className="flex h-full w-full flex-col items-center justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.5, rotate: -10 }}
                  whileInView={{ opacity: 1, scale: 1, rotate: 0 }}
                  transition={{ duration: 0.7, ease: "easeOut" }}
                  viewport={{ once: true }}
                >
                  <Image
                    alt="sey_naturelle_logo"
                    src={sn_Logo}
                    className="w-36"
                  />
                </motion.div>
                <motion.div
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={staggerContainer}
                  className="-mt-8 flex w-full max-w-lg flex-col items-center p-4 text-center"
                >
                  <motion.h2
                    variants={fadeInUp}
                    className={cn(
                      aldineBT.className,
                      "pb-2 text-4xl font-medium",
                    )}
                  >
                    Sey Naturelle
                  </motion.h2>
                  <motion.p variants={fadeInUp}>
                    Sey Naturelle is a chemical free cosmetic line carefully
                    formulated with Ayurveda herbs tried and tested to provide
                    exceptional benefits to wellbeing of the skin.
                  </motion.p>
                  <motion.div
                    variants={fadeInUp}
                    className="mt-2 flex items-center space-x-2"
                  >
                    <Link
                      href={"/sey-naturelle"}
                      className={cn(
                        buttonVariants({
                          variant: "default",
                          size: "withIconRight",
                        }),
                        "group space-x-2 bg-[#C9EF50] hover:bg-[#C9EF50]",
                      )}
                    >
                      <span>View Sey Naturelle</span>
                      <div className="flex items-center justify-center pl-2">
                        <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                        <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                      </div>
                    </Link>
                    <Link
                      href={"/store#sey-nanturelle"}
                      className={cn(
                        buttonVariants({
                          variant: "default",
                          size: "withIconRight",
                        }),
                        "group space-x-2",
                      )}
                    >
                      <span>Browse In Shop</span>
                      <div className="flex items-center justify-center pl-2">
                        <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                        <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                      </div>
                    </Link>
                  </motion.div>
                </motion.div>
              </div>
              {/* down */}
              <motion.div
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUp}
                className="p-4"
              >
                <Carousel
                  opts={{
                    align: "start",
                  }}
                  className="relative w-full"
                >
                  <CarouselContent className="pt-4">
                    {seyNaturelleProducts.slice(0, 6).map((p, index) => (
                      <CarouselItem
                        key={index}
                        className="md:basis-1/3 lg:basis-1/4"
                      >
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          whileInView={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          viewport={{ once: true }}
                          className="flex flex-col"
                        >
                          <motion.div
                            whileHover={{
                              y: -5,
                              boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)",
                            }}
                            transition={{ duration: 0.2 }}
                            className="flex w-full flex-col rounded-3xl border border-gray-200 bg-white p-2"
                          >
                            <motion.div>
                              <ImageComponent
                                image={p.coverImage}
                                className="h-[17rem] rounded-2xl object-cover"
                              />
                            </motion.div>
                            <div className="flex flex-col items-center space-y-2 pt-2 text-center">
                              <p
                                className={cn(
                                  aldineBT.className,
                                  "text-xl font-medium",
                                )}
                              >
                                {p.packageName}
                              </p>
                              <span>{currency(p.price, "GHS")}</span>
                              <motion.div className="w-full">
                                <Link
                                  href={`/store/${p.slug}`}
                                  className="block w-full rounded-full bg-black px-4 py-2 text-white"
                                >
                                  <span>Buy Now</span>
                                </Link>
                              </motion.div>
                            </div>
                          </motion.div>
                        </motion.div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                  {/* left */}
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="absolute inset-y-1 left-0 flex flex-col items-center justify-center p-4"
                  >
                    <CarouselMinus className="border border-black bg-black text-white hover:bg-gray-900 hover:text-white" />
                  </motion.div>
                  {/* right */}
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="absolute inset-y-1 right-0 flex flex-col items-center justify-center p-4"
                  >
                    <CarouselPlus className="border border-black bg-black text-white hover:bg-gray-900 hover:text-white" />
                  </motion.div>
                </Carousel>
              </motion.div>
            </motion.div>
          </section>
          {/* testimonials */}
          <Testimonials />
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
