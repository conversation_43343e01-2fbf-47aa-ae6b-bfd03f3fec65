@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  /* Soft Neutrals */
  --color-ivory: oklch(99% 0.02 100);
  --color-taupe: oklch(45% 0.08 60);
  --color-gray-50: oklch(98% 0.01 100);
  --color-gray-500: oklch(55% 0.05 75);
  
  /* Black Cherry Accent */
  --color-primary: oklch(30% 0.15 350);
  --color-primary-400: oklch(50% 0.18 345);
  
  
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  
  @keyframes accordion-down {
    from {
      height: 0;
    }
    
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    
    to {
      height: 0;
    }
  }
}


/*
The default border color has changed to `currentColor` in Tailwind CSS v4,
so we've added these compatibility styles to make sure everything still
looks the same as it did with Tailwind CSS v3.

If we ever want to remove these styles, we need to add an explicit border
color utility to any element that depends on these defaults.
*/

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

@layer utilities {
  .bg-glow-gradient {
    background-image: linear-gradient(
    in oklch,
    oklch(99% 0.02 100),
    oklch(92% 0.04 110)
    );
  }
}