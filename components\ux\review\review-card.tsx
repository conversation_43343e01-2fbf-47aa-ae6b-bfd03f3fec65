import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Database } from "@/types/supabase";
import { StarSolidIcon } from "../icons";

type review = Database["public"]["Tables"]["reviews"]["Row"];

export default function ReviewsCard({ r }: { r: review }) {
  return (
    <div className='flex w-full basis-1/2 flex-col p-2'>
      <div className='relative flex flex-col items-center space-y-2 rounded-3xl border bg-white p-4 text-center hover:drop-shadow-xs'>
        <div className='flex items-center justify-center space-x-1'>
          {[...Array(5)].map((_, i) => {
            const ratingValue = i + 1;

            return (
              <StarSolidIcon
                key={i}
                className={cn(
                  "size-5",
                  ratingValue <= r.review! ? "text-yellow-400" : "text-gray-300"
                )}
              />
            );
          })}
        </div>
        <p className='mt-4 text-2xl'>{r.user_name}</p>
        {r.type && (
          <p className={cn(buttonVariants({ size: "sm" }), "w-fit")}>
            {r.type}
          </p>
        )}
        <p className='ext-sm text-neutral-500'>{r.review_message}</p>
      </div>
    </div>
  );
}
