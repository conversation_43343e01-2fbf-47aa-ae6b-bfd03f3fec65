"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { FolderOpen, Mail, User } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { StarSolidIcon } from "@/components/ux/icons";
import { supabaseClient } from "@/lib/supabase/client";

export const formSchema = z.object({
  customer_name: z.string().min(2, {
    error: "full name is required.",
  }),
  customer_email: z.email({
    error: "Email is required.",
  }),
  rating: z.number().min(1, {
    error: "You forgot to select a star rating.",
  }),
  message: z.string().min(2, {
    error: "You forgot to add a review message.",
  }),
  review_type: z.string().min(2, {
    error: "You forgot to add a reference.",
  }),
});

export default function ReviewsForm() {
  const router = useRouter();
  const [rating, setRating] = useState(0);
  const [submit, setSubmit] = useState("Submit");

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      customer_name: "",
      customer_email: "",
      review_type: "",
      rating: 0,
      message: "",
    },
  });

  const onSubmit = (d: z.infer<typeof formSchema>) => {
    return new Promise(async (resolve, reject) => {
      const supabase = supabaseClient();

      setSubmit("Submitting");

      const { data, error } = await supabase.from("reviews").insert({
        user_name: d.customer_name,
        user_email: d.customer_email,
        review: d.rating,
        review_message: d.message,
        type: d.review_type,
      });

      if (error) {
        setSubmit("Try Again");
        reject(error);
      } else {
        await fetch("/api/review-confirmation", {
          method: "POST",
          headers: {
            "Content-Type": "application/json", // Fix typo: "Aplication/json" to "application/json"
          },
          body: JSON.stringify(d),
        });

        resolve(d);
        setSubmit("Submitted");
        router.refresh();
      }
    });
  };

  return (
    <div className="my-10 flex w-full flex-col items-center">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => {
            return toast.promise(onSubmit(data), {
              loading: "Submitting...",
              success: () => `Review submitted`,
              error: (err: any) => `Error: ${err.message}`,
            });
          })}
          className="my-4 w-full space-y-4 md:w-80"
        >
          <FormField
            control={form.control}
            name="rating"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex items-center justify-center space-x-2 text-center">
                    {[...Array(5)].map((_, i) => {
                      const ratingValue = i + 1;

                      return (
                        <StarSolidIcon
                          key={i}
                          onClick={() => {
                            setRating(ratingValue);
                            form.setValue("rating", ratingValue);
                          }}
                          // style={{
                          //   color: ratingValue <= rating ? 'gold' : 'grey'
                          // }}
                          className={cn(
                            "size-8",
                            ratingValue <= rating
                              ? "text-yellow-400"
                              : "text-gray-300",
                          )}
                        />
                      );
                    })}
                  </div>
                </FormControl>
                <FormMessage className="w-full items-center pl-2 text-center font-light" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="customer_name"
            render={({ field }) => (
              <FormItem>
                {/* <FormLabel className='pl-2'>Email</FormLabel> */}
                <FormControl>
                  <div className="relative flex w-full">
                    <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <User className="h-[18px] w-[18px] text-gray-400" />
                    </span>
                    <Input
                      className="w-full"
                      withIcon
                      placeholder="Full Name"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage className="pl-2 font-light" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="customer_email"
            render={({ field }) => (
              <FormItem>
                {/* <FormLabel className='pl-2'>Password</FormLabel> */}
                <FormControl>
                  <div className="relative flex h-10 w-full">
                    <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <Mail className="h-[18px] w-[18px] text-gray-400" />
                    </span>
                    <Input
                      className="w-full"
                      withIcon
                      placeholder="Email"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage className="pl-2 font-light" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="review_type"
            render={({ field }) => (
              <FormItem>
                {/* <FormLabel className='pl-2'>Password</FormLabel> */}
                <FormControl>
                  <div className="relative flex h-10 w-full">
                    <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                      <FolderOpen className="h-[18px] w-[18px] text-gray-400" />
                    </span>
                    <Input
                      className="w-full"
                      withIcon
                      placeholder="Reference (eg. Courses or Makeup)"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage className="pl-2 font-light" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                {/* <FormLabel className='pl-2'>Password</FormLabel> */}
                <FormControl>
                  <Textarea
                    className="w-full"
                    placeholder="Your Message"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="pl-2 font-light" />
              </FormItem>
            )}
          />

          <Button
            variant={"outline"}
            className="h-10 w-full rounded-3xl"
            type="submit"
          >
            {submit}
          </Button>
        </form>
      </Form>
    </div>
  );
}
