import Footer from "@/components/footer";
import Navbar from "@/components/layouts/navbar";
import ImageComponent from "@/components/studio/img";
import Description from "@/components/studio/post-body";
import { buttonVariants } from "@/components/ui/button";
import ParallaxScroll from "@/components/ux/about/parallax-scroll";
import { FounderProfile } from "@/components/ux/about/founder-profile";
import { FONT_PLAYFAIR_DISPLAY, aldineBT } from "@/constants/fonts";
import { getAbout } from "@/lib/sanity/lib/actions";
import { cn } from "@/lib/utils";
import { aboutType } from "@/types/types";
import { ChevronRight } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

export const revalidate = 30;

export const metadata: Metadata = {
  title: "About",
  description:
    "About Us, Learn About Our Aspirations to Inspire Beauty and Confidence.",
  openGraph: {
    title: "About | Black Cherry",
    description:
      "About Us, Learn About Our Aspirations to Inspire Beauty and Confidence.",
    url: "https://www.blackcherrygh.com/about/",
    siteName: "About | Black Cherry",
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "About | Black Cherry",
    description:
      "About Us, Learn About Our Aspirations to Inspire Beauty and Confidence.",
    site: "https://www.blackcherrygh.com/about/",
  },
};

export default async function About() {
  const about: aboutType = await getAbout();
  // console.log(about);
  const parallaxImages = [
    about.image,
    about.image1,
    about.image2,
    about.image3,
    about.image4,
    about.image5,
  ];
  return (
    <main>
      {/* hero section */}
      <section className="flex w-full flex-col items-center">
        <div className="relative z-10 mb-10 flex h-[42rem] w-full flex-col items-center justify-center overflow-hidden bg-gray-50">
          <div className="relative w-full">
            <div className="absolute inset-0 bg-black/70" />
            <ImageComponent
              image={about.image}
              className="h-[42rem] object-cover"
            />
          </div>
          <div className="absolute bottom-4 flex h-full w-full max-w-lg flex-col items-center justify-end p-4 text-center">
            <div className="flex flex-col items-center space-y-2 text-white">
              <h2 className="text-5xl">About Us</h2>
              <p>Reach Out and Connect with Blackcherry</p>
              <div className="pt-4">
                <Link
                  href={"/contact"}
                  className={cn(
                    buttonVariants({
                      variant: "default",
                      size: "withIconRight",
                    }),
                    "group space-x-2",
                  )}
                >
                  <span>Contact Us</span>
                  <div className="flex items-center justify-center pl-2">
                    <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                    <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>{" "}
      {/* Vision Statement */}
      <section className="mt-8 flex w-full flex-col items-center">
        <div className="flex w-full max-w-2xl flex-col items-center p-4">
          <div className="space-y-4 text-center">
            <Description
              content={about.description}
              className="text-center text-xl tracking-wide"
            />
          </div>
        </div>
      </section>
      {/* Founder Profile Section */}
      <FounderProfile />
      {/* Values and Mission */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="mb-12 text-center">
              <h2
                className={cn(
                  FONT_PLAYFAIR_DISPLAY.className,
                  "mb-4 text-3xl font-medium",
                )}
              >
                Our Values & Mission
              </h2>
              <p className="text-gray-600">
                Empowering women through beauty, wellness, and education
              </p>
            </div>
            <div className="grid gap-8 md:grid-cols-3">
              <div className="p-6 text-center">
                <h3
                  className={cn(aldineBT.className, "mb-4 text-xl font-medium")}
                >
                  Natural Beauty
                </h3>
                <p className="text-gray-600">
                  Embracing and enhancing your natural beauty with
                  chemical-free, healthy products
                </p>
              </div>
              <div className="p-6 text-center">
                <h3
                  className={cn(aldineBT.className, "mb-4 text-xl font-medium")}
                >
                  Expert Care
                </h3>
                <p className="text-gray-600">
                  Professional beauty services tailored to high melanin skin
                  types
                </p>
              </div>
              <div className="p-6 text-center">
                <h3
                  className={cn(aldineBT.className, "mb-4 text-xl font-medium")}
                >
                  Education
                </h3>
                <p className="text-gray-600">
                  Empowering through knowledge and professional training
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Image Gallery */}
      <section className="mt-8 flex w-full flex-col items-center">
        {/* <ParallaxScroll imgs={parallaxImages} /> */}
      </section>
      <section className="mt-8 mb-10 flex w-full flex-col items-center">
        <div className="flex w-full max-w-xl flex-col items-center p-4">
          {/* descrition 1 */}
          <div>
            <Description
              content={about.description1}
              className="text-center text-xl tracking-wide"
            />
          </div>
        </div>
      </section>
    </main>
  );
}
