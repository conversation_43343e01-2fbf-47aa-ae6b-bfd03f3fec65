import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { buttonVariants } from "@/components/ui/button";
import { getFaqs } from "@/lib/sanity/lib/actions";
import { cn } from "@/lib/utils";
import { faq } from "@/types/types";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

export default async function FAQS() {
  const faqs: faq[] = await getFaqs();
  return (
    <div className="flex w-full flex-col items-center">
      <div className="flex w-full max-w-xl flex-col items-center p-6">
        {/* title */}
        <div className="flex w-full flex-col items-center">
          <div className="my-4 flex w-full flex-col items-center text-center">
            <h2 className="text-6xl font-bold">
              <span className="sm:hidden">FAQs</span>
              <span className="hidden sm:block">
                Frequently Asked Questions
              </span>
            </h2>
            <p>Answers to Your Questions</p>
            <div className="py-4">
              <Link
                href={"/reviews/new"}
                className={cn(
                  buttonVariants({
                    variant: "default",
                    size: "withIconRight",
                  }),
                  "group space-x-2 bg-black text-white hover:bg-gray-900",
                )}
              >
                <span>Leave A Review</span>
                <div className="flex items-center justify-center pl-2">
                  <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                  <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-white" />
                </div>
              </Link>
            </div>
          </div>
        </div>
        {/* constent */}
        <div className="flex w-full flex-col">
          {faqs.map((faq) => {
            return (
              <div key={faq.orderid} className="flex w-full flex-col">
                <Accordion type="single" collapsible>
                  <AccordionItem value="item-1">
                    <AccordionTrigger className="text-start">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent>{faq.answer}</AccordionContent>
                  </AccordionItem>
                </Accordion>
              </div>
            );
          })}
          <Accordion type="single" collapsible>
            <AccordionItem value="item-1">
              <AccordionTrigger>Is it accessible?</AccordionTrigger>
              <AccordionContent>
                Yes. It adheres to the WAI-ARIA design pattern.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </div>
  );
}
