import type { NextConfig } from "next";

const nextConfig: NextConfig = {
   images: {
    remotePatterns: [
      { hostname: "cdn.sanity.io" },
      { hostname: "source.unsplash.com" },
    ],
    formats: ["image/webp"],
  },
  async redirects() {
    return [
      {
        source: "/gallery",
        destination: "/gallery/weddings",
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
