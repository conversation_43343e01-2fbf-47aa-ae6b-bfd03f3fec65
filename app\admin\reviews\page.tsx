import { ReviewsBackendTable } from "@/components/admin/reviews-table";
import { aldineBT } from "@/constants/fonts";
import { supabaseServer } from "@/lib/supabase/server";
import { getAuthenticatedUser } from "@/lib/supabase/session";
import { cn } from "@/lib/utils";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";

export default async function Page() {
  const supabase = await supabaseServer();
  const user = await getAuthenticatedUser();

  if (!user) {
    redirect("/admin/login");
  }

  const { data, error } = await supabase.from("reviews").select("*");
  console.log(error);
  return (
    <main className="min-h-[38rem] p-4">
      <div>
        <div className="flex w-full flex-col items-center">
          <div className="flex w-full max-w-xl flex-col p-4">
            <h2
              className={cn(
                aldineBT.className,
                "text-center text-4xl font-bold",
              )}
            >
              Reviews
            </h2>
          </div>
        </div>
        <div>
          {error && !data ? (
            <div>error</div>
          ) : (
            <div>
              <ReviewsBackendTable data={data!} />
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
