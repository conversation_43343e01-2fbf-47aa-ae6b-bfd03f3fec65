//@ts-nocheck
import { defineField, defineType } from "sanity";
import { Leaf } from "lucide-react";

export default defineType({
  name: "bc360content",
  title: "BC 360",
  icon: Leaf,
  type: "document",
  fields: [
    defineField({
      name: "orderid",
      title: "Order ID",
      type: "number",
    }),
    defineField({
      name: "title",
      title: "Title",
      type: "string",
    }),
    defineField({
      name: "slug",
      title: "Slug",
      description: "Always genereate to get the slug from the title",
      type: "slug",
      options: {
        source: "title",
        maxLength: 112,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "subtitle",
      title: "Sub Title",
      type: "text",
    }),
    // SECTION ONE
    defineField({
      name: "imageone",
      title: "First Image",
      description: "The First Image",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
          initialValue: "img",
          validation: (Rule) => Rule.required(),
        },
      ],
    }),
    defineField({
      name: "descriptionone",
      title: "First Description",
      description: "The description For The first Image",
      type: "array",
      of: [{ type: "block" }],
    }),
    // SECTION TWO
    defineField({
      name: "imagetwo",
      title: "2nd Image",
      description: "The 2nd Image",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
          initialValue: "img",
          validation: (Rule) => Rule.required(),
        },
      ],
    }),
    defineField({
      name: "descriptiontwo",
      title: "2nd Description",
      description: "The description For The 2nd Image",
      type: "array",
      of: [{ type: "block" }],
    }),
    // SECTION THREE
    defineField({
      name: "imagethree",
      title: "3rd Image",
      description: "The 3rd Image",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
          initialValue: "img",
          validation: (Rule) => Rule.required(),
        },
      ],
    }),
    defineField({
      name: "descriptionthree",
      title: "3rd Description",
      description: "The description For The 3rd Image",
      type: "array",
      of: [{ type: "block" }],
    }),
    defineField({
      name: "packages",
      title: "Packages and Prices",
      type: "array",
      of: [
        {
          name: "title",
          title: "Package Name",
          description: "The Package Name",
          type: "object",
          fields: [
            defineField({
              name: "packagename",
              title: "Package Name",
              type: "string",
            }),
            defineField({
              name: "price",
              title: "Price",
              type: "number",
            }),
          ],
        },
      ],
    }),
  ],
  preview: {
    select: {
      title: "title",
      subtitle: "subtitle",
      media: "imageone",
    },
    prepare(selection) {
      return { ...selection };
    },
  },
});
