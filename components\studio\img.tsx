import Image from "next/image";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { Img } from "@react-email/components";
import { urlFor } from "@/lib/sanity/lib/image";

const imageVariants = cva("z-0", {
  variants: {
    variant: {
      thumbnail: "rounded-md",
      coverimg: "",
      postcoverimg: "rounded-md",
      icon: "rounded-full",
    },
    size: {
      thumbnail: "w-24",
      coverimg: "w-full",
      postcoverimg: "w-full",
      icon: "h-10 w-10",
      icon2: "w-full",
    },
  },
  defaultVariants: {
    variant: "coverimg",
    size: "coverimg",
  },
});
interface CoverImageProps extends VariantProps<typeof imageVariants> {
  image: any | undefined;
  gradient?: boolean;
  className?: string;
}

export default function ImageComponent(props: CoverImageProps) {
  const { image: source, size, variant, className } = props;
  const { alt, dimensions } = source;
  // console.log(alt,dimensions);
  return (
    <div className={cn(imageVariants({ variant }))}>
      <Image
        className={cn(className, imageVariants({ size }))}
        width={dimensions ? dimensions.width : 550}
        height={dimensions ? dimensions.height : 400}
        alt={alt ? alt : "img"}
        src={urlFor(source)
          .height(dimensions ? dimensions.height : 402)
          .width(dimensions ? dimensions.width : 534)
          .url()}
        sizes="100vw"
      />
    </div>
  );
}

export function EmailImageComponent(props: CoverImageProps) {
  const { image: source, size, variant, className } = props;
  const { alt, dimensions } = source;
  // console.log(alt,dimensions);
  return (
    <Img
      src={urlFor(source).height(285).width(250).url()}
      style={{
        marginTop: "16px",
        objectFit: "cover",
        objectPosition: "bottom",
        width: "250",
        height: "285",
      }}
      width={250}
      height={285}
      alt={alt ? alt : "img"}
    />
  );
}
export function ImageComponentWithSize(props: CoverImageProps) {
  const { image: source, size, variant, className } = props;
  const { alt, dimensions } = source;
  // console.log(alt,dimensions);
  return (
    <div className={cn(imageVariants({ variant }))}>
      <Image
        className={cn(className, imageVariants({ size }))}
        width={dimensions ? dimensions.width : 550}
        height={dimensions ? dimensions.height : 400}
        alt={alt ? alt : "img"}
        src={urlFor(source)
          .height(dimensions ? dimensions.height : 402)
          .width(dimensions ? dimensions.width : 534)
          .url()}
        sizes="100vw"
      />
    </div>
  );
}
