import { rateCardType } from "@/types/types";
import ImageComponent from "../studio/img";
import Description from "../studio/post-body";
import { cn } from "@/lib/utils";
import { aldineBT } from "@/constants/fonts";
import { currency } from "@/hooks/use-currency";

export default function RateCard({ rate }: { rate: rateCardType }) {
  if (rate.ratetype === "package") {
    return (
      <div className="flex h-full w-full flex-col rounded-3xl border p-2">
        {/* img */}
        <div>
          <ImageComponent image={rate.coverImage} className="rounded-xl" />
        </div>
        {/* package */}
        <div className="flex h-full flex-col space-y-2 p-2">
          {/* title */}
          <h2 className={cn(aldineBT.className, "text-4xl font-bold")}>
            {rate.packageName}
          </h2>
          <p className={cn(aldineBT.className, "text-xl")}>
            {currency(rate.price, "GHS")}
          </p>
          <div>
            <Description content={rate.features} className="text-sm" />
          </div>
          {/* price */}
        </div>
      </div>
    );
  } else if (rate.ratetype === "single") {
    return (
      <div className="flex items-center justify-between border bg-gray-100 p-4 opacity-60 transition-all duration-300 ease-linear hover:border-black hover:opacity-100 hover:shadow-xs">
        <h2 className={cn(aldineBT.className, "text-2xl font-bold")}>
          {rate.packageName}
        </h2>
        <p className="text-lg font-medium">{currency(rate.price, "GHS")}</p>
      </div>
    );
  } else {
    return (
      <div>
        <div>
          <p>Not rate type</p>
        </div>
      </div>
    );
  }
}
