"use client";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { currency } from "@/hooks/use-currency";
import {
  Banknote,
  ChevronRight,
  FlaskRound,
  GraduationCap,
  Leaf,
  Store,
  Ticket,
} from "lucide-react";
import Link from "next/link";
import ReactPlayer from "react-player";

let videosrc = "https://www.youtube.com/watch?v=RJOuQ-9D94w";

const classFeatures = [
  {
    icon: <Store className="h-4 w-4" />,
    id: 1,
    title: "Business Administration",
  },
  {
    icon: <GraduationCap className="h-4 w-4" />,
    id: 2,
    title: "Aesthetic Specialist Certificate",
  },
  {
    icon: <FlaskRound className="h-4 w-4" />,
    id: 3,
    title: "Cosmetic Science",
  },
  { icon: <Leaf className="h-4 w-4" />, id: 4, title: "Lash Technology" },
];
export default function CourseHeroSection() {
  return (
    <div className="mt-16 flex w-full flex-col items-center p-4">
      <section className="relative z-10 flex aspect-video h-[38rem] w-full max-w-7xl flex-col overflow-hidden rounded-[2.5rem] bg-black md:h-auto">
        <div className="absolute inset-0 z-20 object-cover">
          <div className="relative z-20 aspect-video w-full object-cover">
            <div className="bg-opacity-60 backdrop-blur-0 absolute inset-0 bg-black backdrop-filter" />
            <ReactPlayer
              width="100%"
              height="100%"
              url={videosrc}
              loop
              controls={false}
              playing={true}
              muted={true}
            />
          </div>
        </div>
        <div className="absolute inset-0 z-30 flex h-full w-full flex-col items-center justify-end">
          <div className="flex max-w-2xl flex-col justify-end p-2">
            <div className="flex flex-col items-center space-y-2 p-4 text-center text-white">
              <h2 className="text-5xl">Beauty Business Academy</h2>
              <p>
                we provide top-notch beauty training and essential business
                skills to empower our students for success in the industry. With
                certified courses and flexible options, we cater to your
                individual needs.
              </p>
              <div className="pt-4">
                <Link
                  href={"#registration-form"}
                  className={cn(
                    buttonVariants({
                      variant: "default",
                      size: "withIconRight",
                    }),
                    "group space-x-2",
                  )}
                >
                  <span>Register Now</span>
                  <div className="flex items-center justify-center pl-2">
                    <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                    <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="my-4 flex w-full max-w-4xl flex-col items-center">
        <div className="flex flex-wrap items-center justify-center">
          {classFeatures.map((f) => (
            <Button
              key={f.id}
              variant={"outline"}
              size={"withIconLeft"}
              className="group m-1 space-x-2"
            >
              <span>{f.icon}</span>
              <span>{f.title}</span>
            </Button>
          ))}
        </div>
      </section>
    </div>
  );
}
