import { StoreBackendTable } from "@/components/admin/store-table";
import { aldineBT } from "@/constants/fonts";
import { supabaseServer } from "@/lib/supabase/server";
import { getAuthenticatedUser } from "@/lib/supabase/session";
import { cn } from "@/lib/utils";
import { redirect } from "next/navigation";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function Page() {
  const supabase = await supabaseServer();
  const user = await getAuthenticatedUser();

  if (!user) {
    redirect("/admin/login");
  }

  const { data, error } = await supabase.from("store_backend").select("*");
  return (
    <main className="min-h-[38rem] p-4">
      <div className="flex w-full flex-col">
        <div className="flex w-full flex-col items-center">
          <div className="flex w-full max-w-xl flex-col p-4">
            <h2
              className={cn(
                aldineBT.className,
                "text-center text-4xl font-bold",
              )}
            >
              Store
            </h2>
          </div>
        </div>
        <div className="flex w-full flex-col items-center">
          <div className="flex w-full max-w-3xl flex-col">
            {error && !data ? (
              <div>error:{error.message}</div>
            ) : (
              <div>
                <StoreBackendTable data={data} />
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
