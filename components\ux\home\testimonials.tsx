"use client";

import { buttonVariants } from "@/components/ui/button";
import { testimonials } from "@/constants/db";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useRef, useMemo } from "react";
import { StarSolidIcon } from "@/components/ux/icons";
import { FONT_PLAYFAIR_DISPLAY } from "@/constants/fonts";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";

// Define the testimonial type
type TestimonialWithRating = {
  id: number;
  name: string;
  message: string;
  rating: number;
};

// Add deterministic ratings to testimonials based on ID
const testimonialsWithRatings: TestimonialWithRating[] = testimonials.map(
  (testimonial) => ({
    ...testimonial,
    // Use a deterministic rating based on ID to avoid hydration errors
    rating: testimonial.id % 2 === 0 ? 5 : 4,
  }),
);

export default function Testimonials() {
  const carouselRef = useRef(null);

  // Group testimonials into sets of 3 for desktop view
  const testimonialGroups = useMemo(() => {
    const groups = [];
    for (let i = 0; i < testimonialsWithRatings.length; i += 3) {
      groups.push(testimonialsWithRatings.slice(i, i + 3));
    }
    return groups;
  }, []);

  return (
    <section className="relative w-full overflow-hidden bg-gradient-to-b from-white via-gray-50/30 to-white py-16">
      <div className="mx-auto flex w-full max-w-7xl flex-col items-center px-4">
        {/* Section header */}
        <div className="mb-12 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative mb-2"
          >
            <h2
              className={cn(
                FONT_PLAYFAIR_DISPLAY.className,
                "text-4xl font-bold md:text-5xl",
              )}
            >
              Testimonials
            </h2>
          </motion.div>

          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-6 text-lg text-gray-600"
          >
            Discover What Our Clients Have to Say
          </motion.p>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Link
              href={"/reviews/new"}
              className={cn(
                buttonVariants({
                  variant: "default",
                  size: "withIconRight",
                }),
                "group space-x-2 bg-black text-white hover:bg-gray-900",
              )}
            >
              <span>Leave A Review</span>
              <div className="flex items-center justify-center pl-2">
                <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-white" />
                <ChevronRight className="h-5 w-5 text-white transition-all duration-300 ease-linear" />
              </div>
            </Link>
          </motion.div>
        </div>

        {/* Desktop Carousel */}
        <div className="hidden w-full md:block">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            plugins={[
              Autoplay({
                delay: 5000,
                stopOnInteraction: true,
              }),
            ]}
            className="w-full"
            ref={carouselRef}
          >
            <CarouselContent className="py-4">
              {testimonialGroups.map((group, groupIndex) => (
                <CarouselItem key={groupIndex}>
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    {group.map((testimonial, index) => (
                      <TestimonialCard
                        key={testimonial.id}
                        testimonial={testimonial}
                        index={index}
                      />
                    ))}
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="mt-8 flex items-center justify-center gap-2">
              <CarouselPrevious className="static translate-x-0 translate-y-0" />
              <CarouselNext className="static translate-x-0 translate-y-0" />
            </div>
          </Carousel>
        </div>

        {/* Mobile Carousel - one testimonial at a time */}
        <div className="w-full md:hidden">
          <Carousel
            opts={{
              align: "center",
              loop: true,
            }}
            plugins={[
              Autoplay({
                delay: 5000,
                stopOnInteraction: true,
              }),
            ]}
            className="w-full"
          >
            <CarouselContent>
              {testimonialsWithRatings.map((testimonial) => (
                <CarouselItem key={testimonial.id}>
                  <div className="px-2">
                    <TestimonialCard
                      testimonial={testimonial}
                      index={0}
                      isMobile
                    />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="mt-8 flex items-center justify-center gap-2">
              <CarouselPrevious className="static translate-x-0 translate-y-0" />
              <CarouselNext className="static translate-x-0 translate-y-0" />
            </div>
          </Carousel>
        </div>
      </div>
    </section>
  );
}

function TestimonialCard({
  testimonial,
  index,
  isMobile = false,
}: {
  testimonial: TestimonialWithRating;
  index: number;
  isMobile?: boolean;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20, filter: "blur(8px)" }}
      whileInView={{ opacity: 1, y: 0, filter: "blur(0px)" }}
      exit={{ opacity: 0, filter: "blur(8px)" }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className={cn(
        "group flex flex-col justify-between rounded-2xl border border-gray-200 bg-white p-6 transition-all duration-300 hover:shadow-md md:h-[200px]",
        isMobile ? "min-h-[200px]" : "min-h-[280px]",
      )}
    >
      <div>
        <div className="mb-4 flex items-center justify-center space-x-1">
          {[...Array(5)].map((_, i) => (
            <StarSolidIcon
              key={i}
              className={cn(
                "h-4 w-4",
                i < testimonial.rating ? "text-yellow-400" : "text-gray-200",
              )}
            />
          ))}
        </div>

        <div className="px-1">
          <div className="text-center leading-relaxed tracking-wide text-gray-700 md:text-base">
            &ldquo;{testimonial.message}&rdquo;
          </div>
        </div>
      </div>

      <div className="mt-4 flex items-center justify-center">
        <motion.div
          className="h-1 w-12 rounded-full bg-gray-200 transition-all duration-300 group-hover:bg-black"
          whileInView={{ width: [0, 48] }}
          transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
          viewport={{ once: true }}
        />
      </div>
    </motion.div>
  );
}
