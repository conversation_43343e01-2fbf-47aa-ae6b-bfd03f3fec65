"use client";
import ImageComponent from "@/components/studio/img";
import { Button } from "@/components/ui/button";
import { aldineBT } from "@/constants/fonts";
import { useCartStore } from "@/constants/store/cart";
import { currency } from "@/hooks/use-currency";
import { cn } from "@/lib/utils";
import { seyNaturelleProductdType } from "@/types/types";
import { ShoppingCart } from "lucide-react";
import { useState } from "react";

type pTType = "sey-naturelle" | "i-adore" | "store";

export default function PackageCard({
  product,
  productType,
}: {
  product: seyNaturelleProductdType;
  productType: pTType;
}) {
  const { coverImage, packageName, price, slug, stock } = product;
  const { addToCart, cartItems, clearCart, removeFromCart } = useCartStore(
    (state) => state,
  );
  const [viewProduct, setViewProduct] = useState(false);

  const produtLinkRoute = (slug: string) => {
    if (productType === "sey-naturelle") {
      return `/store/sey-naturelle/${slug}`;
    } else if (productType === "i-adore") {
      return `/store/i-adore/${slug}`;
    } else {
      return `/store/${slug}`;
    }
  };
  return (
    <div id={slug} className="flex flex-col">
      <div className="flex w-full flex-col rounded-3xl border border-gray-200 bg-gray-100 p-2 hover:shadow-sm">
        <div
          // href={produtLinkRoute(slug)}
          onMouseEnter={() => setViewProduct(true)}
          onMouseLeave={() => setViewProduct(false)}
          className="relative overflow-hidden rounded-xl"
        >
          <ImageComponent
            image={coverImage}
            className="aspect-square object-cover"
          />

          {/* <div
            className={cn(
              "absolute inset-0 flex h-full w-full flex-col items-center justify-center bg-black/10 transition-all duration-300",
              !viewProduct ? "opacity-0" : "opacity-100",
            )}
          >
            <Button>
              <p>view product</p>
            </Button>
          </div> */}
        </div>
        <div className="flex flex-col items-center space-y-2 pt-2 text-center">
          <p className={cn(aldineBT.className, "text-xl font-medium")}>
            {packageName}
          </p>
          <span>{currency(price, "GHS")}</span>
          {stock === "out-of-stock" ? (
            <div
              // href={produtLinkRoute(slug)}
              className="hover: w-full rounded-full bg-red-600 px-4 py-2 text-white"
            >
              <span>Out Of Stock</span>
            </div>
          ) : (
            <div className="flex w-full flex-col">
              {/* <Link
              // href={produtLinkRoute(slug)}
              className='w-full bg-black text-white px-4 py-2 rounded-full'
              >
              <span> Buy Now</span>
            </Link> */}
              {!cartItems.includes(product) ? (
                <Button
                  onClick={() => addToCart(product)}
                  className="items-center space-x-2 bg-black text-white hover:bg-black/90"
                >
                  <ShoppingCart className="size-4" />
                  <span>Add To Cart</span>
                </Button>
              ) : (
                <Button
                  variant={"destructive"}
                  onClick={() => removeFromCart(product._id)}
                  className="space-x-2"
                >
                  <ShoppingCart className="size-4" />
                  <span>Added | Remove From Cart</span>
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
