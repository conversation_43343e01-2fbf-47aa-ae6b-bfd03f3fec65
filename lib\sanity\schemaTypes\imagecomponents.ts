import { ImageIcon } from 'lucide-react'
import { defineType } from 'sanity'

export default defineType({
 name: 'imagecomponents',
 title: 'Image Components',
 icon: ImageIcon,
 type: 'document',
 fields: [
  {
   name: 'orderid',
   title: 'orderID',
   type: 'number',
   validation: (Rule) => Rule.required(),
  },
  {
   name: 'imageName',
   title: 'Image Name',
   description: "The Name Of The Image Component Should Be Related To Where It Will Be Used!",
   type: 'string',
   validation: (Rule) => Rule.required(),
  },
  {
   name: 'slug',
   title: 'Slug',
   description: 'This is for querry eg.Hero Image',
   type: 'slug',
   options: {
    source: 'imageName',
   },
   validation: (Rule) => Rule.required(),
  },
  {
   name: 'images',
   type: 'array',
   title: 'Images',
   description: "use this if there are more images",
   of: [
    {
     name: 'image',
     type: 'image',
     title: 'Image',
     options: {
      hotspot: true,
     },
     fields: [
      {
       name: 'alt',
       type: 'string',
       title: 'Alternative text',
       initialValue: "img",
       validation: (Rule) => Rule.required(),
      },
     ],
    },
   ],
   options: {
    layout: 'grid',
   },
  },
 ],
 preview: {
  select: {
   title: 'imageName',
   orderid: 'orderid',
   images: "images",
  },
  prepare(selection) {
   const { orderid, images } = selection
   return { ...selection, media: images[0], subtitle: orderid && `order ${orderid}` }
  },
 },
})
