"use client";

import { aldineBT, fibonSans } from "@/constants/fonts";
import { cn } from "@/lib/utils";
import { CVData } from "@/lib/cv-content";
import { motion } from "framer-motion";
import { 
  Briefcase, 
  Calendar, 
  MapPin, 
  ChevronRight,
  Building,
  TrendingUp,
  Users,
  Target
} from "lucide-react";

interface ExperienceSectionProps {
  cvData: CVData;
}

export default function ExperienceSection({ cvData }: ExperienceSectionProps) {
  const { workExperience, projects } = cvData;

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className={cn(aldineBT.className, "text-4xl lg:text-5xl font-bold text-slate-900 mb-6")}>
            Professional Experience
          </h2>
          <div className="w-24 h-1 bg-blue-600 mx-auto mb-8" />
          <p className={cn(fibonSans.className, "text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed")}>
            A track record of delivering results and driving organizational success through 
            strategic leadership, innovative problem-solving, and collaborative teamwork.
          </p>
        </motion.div>

        {/* Work Experience Timeline */}
        <div className="mb-20">
          <h3 className={cn(aldineBT.className, "text-3xl font-bold text-slate-900 mb-12 text-center")}>
            Career Journey
          </h3>
          
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-blue-200 hidden md:block" />
            
            <div className="space-y-12">
              {workExperience.map((job, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  className="relative"
                >
                  {/* Timeline Dot */}
                  <div className="absolute left-6 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg hidden md:block" />
                  
                  {/* Content Card */}
                  <div className="md:ml-20 bg-slate-50 rounded-2xl p-8 hover:shadow-lg transition-shadow">
                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
                      <div className="flex-1">
                        <h4 className={cn(aldineBT.className, "text-2xl font-bold text-slate-900 mb-2")}>
                          {job.title}
                        </h4>
                        <div className="flex items-center space-x-4 text-slate-600 mb-4">
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4" />
                            <span className={cn(fibonSans.className, "font-medium")}>
                              {job.company}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4" />
                            <span className={cn(fibonSans.className)}>
                              {job.location}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full">
                        <Calendar className="h-4 w-4" />
                        <span className={cn(fibonSans.className, "font-medium text-sm")}>
                          {job.duration}
                        </span>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      {job.responsibilities.map((responsibility, respIndex) => (
                        <motion.div
                          key={respIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: (index * 0.2) + (respIndex * 0.1) }}
                          viewport={{ once: true }}
                          className="flex items-start space-x-3"
                        >
                          <ChevronRight className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                          <span className={cn(fibonSans.className, "text-slate-700 leading-relaxed")}>
                            {responsibility}
                          </span>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Key Projects */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className={cn(aldineBT.className, "text-3xl font-bold text-slate-900 mb-12 text-center")}>
            Key Projects & Achievements
          </h3>
          
          <div className="grid lg:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-blue-50 to-slate-50 rounded-2xl p-8 border border-blue-100 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-start justify-between mb-6">
                  <div className="flex-1">
                    <h4 className={cn(aldineBT.className, "text-xl font-bold text-slate-900 mb-2")}>
                      {project.title}
                    </h4>
                    <div className="flex items-center space-x-2 text-blue-600 mb-4">
                      <Calendar className="h-4 w-4" />
                      <span className={cn(fibonSans.className, "font-medium text-sm")}>
                        {project.duration}
                      </span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                </div>
                
                <p className={cn(fibonSans.className, "text-slate-700 mb-4 leading-relaxed")}>
                  {project.description}
                </p>
                
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <TrendingUp className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
                    <div>
                      <span className={cn(fibonSans.className, "font-medium text-slate-900")}>
                        Impact: 
                      </span>
                      <span className={cn(fibonSans.className, "text-slate-700 ml-2")}>
                        {project.outcomes}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <Users className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                    <div>
                      <span className={cn(fibonSans.className, "font-medium text-slate-900")}>
                        Technologies: 
                      </span>
                      <span className={cn(fibonSans.className, "text-slate-700 ml-2")}>
                        {project.technologies}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
