import { buttonVariants } from "@/components/ui/button";
import PackageCard from "@/components/ux/store/package-card";
import { aldineBT } from "@/constants/fonts";
import { iadoreLogo, sn_Logo } from "@/constants/images";
import {
  getAllIAdoreProducts,
  getAllSeyNaturelleProducts,
  getAllStoreProducts,
} from "@/lib/sanity/lib/actions";
import { cn } from "@/lib/utils";
import { seyNaturelleProductdType as productType } from "@/types/types";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export const revalidate = 10;

export default async function Page() {
  const seyNaturelleProducts: productType[] =
    await getAllSeyNaturelleProducts();
  const iadoreProducts: productType[] = await getAllIAdoreProducts();
  const storeProducts: productType[] = await getAllStoreProducts();
  return (
    <div>
      {/* sey naturelle products */}
      <div className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-6xl flex-col space-y-4 p-4 py-20">
          <div id="sey-nanturelle" className="-mt-36 h-24 w-full" />
          <div className="inline-flex items-center p-2">
            <div className="flex w-full flex-col items-center justify-between px-2 py-2 md:flex-row">
              <div
                id="sey-nanturelle"
                className="flex flex-col items-center pr-4 md:flex-row"
              >
                <div className="">
                  <Image
                    alt="sey_naturelle_logo"
                    src={sn_Logo}
                    className="w-12"
                  />
                </div>
                <h1
                  className={cn(aldineBT.className, "py-2 text-2xl font-bold")}
                >
                  Sey Naturelle Products
                </h1>
              </div>
              <div>
                <Link
                  href={"/"}
                  className={cn(
                    buttonVariants({
                      variant: "default",
                      size: "withIconRight",
                    }),
                    "group space-x-2 bg-[#C9EF50] hover:bg-[#C9EF50]",
                  )}
                >
                  <span>View Sey Naturelle</span>
                  <div className="flex items-center justify-center pl-2">
                    <div className="mt-[0.8px] -mr-3 h-[0.1rem] w-0 bg-transparent transition-all duration-300 ease-linear group-hover:w-4 group-hover:bg-black" />
                    <ChevronRight className="text-alt-100 h-5 w-5 transition-all duration-300 ease-linear group-hover:text-black" />
                  </div>
                </Link>
              </div>
            </div>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {seyNaturelleProducts.length === 0 ? (
              <div className="p-4 font-medium">Products Coming Soon</div>
            ) : (
              seyNaturelleProducts.map((p) => {
                return (
                  <div key={p._id}>
                    <PackageCard productType="sey-naturelle" product={p} />
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
      {/* iadore products */}
      <div className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-6xl flex-col space-y-4 border-t p-4 py-20">
          <div id="i-adore" className="-mt-36 h-24 w-full" />
          <div className="inline-flex items-center p-2">
            <div className="flex w-full flex-col items-center justify-between px-2 py-2 md:flex-row">
              <div className="flex flex-col items-center pr-4 md:flex-row">
                <div className="">
                  <Image alt="iadore_logo" src={iadoreLogo} className="w-12" />
                </div>
                <h1
                  className={cn(aldineBT.className, "py-2 text-2xl font-bold")}
                >
                  iAdore Products
                </h1>
              </div>
            </div>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {iadoreProducts.length === 0 ? (
              <div className="p-4 font-medium">Products Coming Soon</div>
            ) : (
              iadoreProducts.map((p) => {
                return (
                  <div key={p._id}>
                    <PackageCard productType="i-adore" product={p} />
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
      {/* other store products */}
      <div className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-6xl flex-col space-y-4 border-t p-4 py-20">
          <div id="store" className="-mt-36 h-24 w-full" />
          <div className="inline-flex items-center p-2">
            <div className="flex w-full flex-col items-center justify-between px-2 py-2 md:flex-row">
              <div className="flex flex-col items-center pr-4 md:flex-row">
                <h1
                  className={cn(aldineBT.className, "py-2 text-2xl font-bold")}
                >
                  Store Products
                </h1>
              </div>
            </div>
          </div>
          <div className="grid gap-6 md:grid-cols-3">
            {storeProducts.length === 0 ? (
              <div className="p-4 font-medium">Products Coming Soon</div>
            ) : (
              storeProducts.map((p) => {
                return (
                  <div key={p._id}>
                    <PackageCard productType="store" product={p} />
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
