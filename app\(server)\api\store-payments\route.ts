import {
  PaymentConfirmationEmail,
  PaymentConfirmationEmailOwner,
} from "@/emails/payment-confirmation";
import { paymentData } from "@/types/types";
import { NextResponse } from "next/server";
import { Resend } from "resend";

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const data: paymentData = await request.json();
  console.log("start");
  console.log(data);
  try {
    const owner = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      // to: ["<EMAIL>"],
      to: ["<EMAIL>"],
      subject: `New Product Purchase`,
      react: PaymentConfirmationEmailOwner({ data }),
    });

    const client = await resend.emails.send({
      from: "Blackcherry | Blackcherry <<EMAIL>>",
      to: [data.payee.email],
      subject: `Purchase Confirmation for ${data.products.length > 1 ? `${data.products[0].packageName} and more` : `${data.products[0].packageName}.`}`,
      react: PaymentConfirmationEmail({ data }),
    });

    console.log("end");
    return NextResponse.json({ owner, client });
  } catch (error) {
    return NextResponse.json({ Error: error });
  }
}
