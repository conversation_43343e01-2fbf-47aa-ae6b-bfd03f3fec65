import { aldineBT } from "@/constants/fonts";
import { supabaseServer } from "@/lib/supabase/server";
import { getAuthenticatedUser } from "@/lib/supabase/session";
import { cn } from "@/lib/utils";
import { redirect } from "next/navigation";

export default async function Page({
  params,
}: {
  params: Promise<{ reference: string }>;
}) {
  const supabase = await supabaseServer();
  const user = await getAuthenticatedUser();
  const reference = (await params).reference;

  if (!user) {
    redirect("/admin/login");
  }

  const { data, error } = await supabase
    .from("new_class_backend")
    .select("*")
    .eq("transaction_id", reference)
    .single();

  return (
    <div className="flex w-full flex-col items-center">
      <section className="flex w-full flex-col items-center">
        <div className="flex w-full max-w-xl flex-col p-4">
          {error && !data ? (
            <div>
              {/* {JSON.stringify(error, null, 2)} */}
              {/* <button
                className='mx-auto mt-4 flex w-full items-center justify-center rounded-full bg-blue-600 p-4 tracking-wide text-white hover:opacity-90'
                onClick={() => reset()}
              >
                Try Again
              </button> */}
            </div>
          ) : (
            <div>
              <div>
                <p className={cn(aldineBT.className, "text-2xl")}>
                  Student&apos;s Details
                </p>
              </div>
              <div className="my-10 space-y-4">
                {/* this si the form detals */}
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">Full Name</p>
                  <p>{data.full_name}</p>
                </div>
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">Email</p>
                  <p>{data.email}</p>
                </div>
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">Age</p>
                  <p>{data.age}</p>
                </div>
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">Phone Number</p>
                  <p>{data.phone_number}</p>
                </div>
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">Whatsapp Phone Number</p>
                  <p>{data.transaction_id}</p>
                </div>
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">Occupation</p>
                  <p>{data.occupation}</p>
                </div>
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">
                    Most Interested Topics
                  </p>
                  <p>
                    {data.nost_interest?.map((d, i) => (
                      <span key={i}>{d},</span>
                    ))}
                  </p>
                </div>
                <div className="flex flex-col space-y-1">
                  <p className="text-lg font-semibold">Most Difficult Aspect</p>
                  <p>
                    {data.difficult_aspect?.map((d, i) => (
                      <span key={i}>{d},</span>
                    ))}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
